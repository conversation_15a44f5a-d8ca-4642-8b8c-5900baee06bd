# Solar Dashboard Environment Configuration
# Copy this file to .env and configure your specific values

# ==========================================
# APPLICATION SETTINGS
# ==========================================

# Flask environment (development/production)
FLASK_ENV=development

# Flask debug mode (0/1)
FLASK_DEBUG=1

# Secret key for session management (CHANGE IN PRODUCTION!)
SECRET_KEY=your_secret_key_here_change_in_production

# Application host and port
HOST=0.0.0.0
PORT=5000

# Logging level (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# ==========================================
# DATABASE CONFIGURATION
# ==========================================

# PostgreSQL database connection
DATABASE_URL=postgresql://postgres:your_db_password@localhost:5432/solar_dashboard

# Database password (used in Docker Compose)
DB_PASSWORD=solar_dashboard_2024

# ==========================================
# REDIS CONFIGURATION (Optional)
# ==========================================

# Redis connection for caching and sessions
REDIS_URL=redis://:your_redis_password@localhost:6379/0
REDIS_PASSWORD=solar_redis_2024

# ==========================================
# DEYE CLOUD API CONFIGURATION
# ==========================================

# Deye Cloud API endpoint (EU region)
DEYE_API_URL=https://eu1-developer.deyecloud.com/v1.0

# Your Deye Cloud credentials
DEYE_USERNAME=your_deye_username_here
DEYE_PASSWORD=your_deye_password_here

# API rate limiting (requests per minute)
DEYE_RATE_LIMIT=60

# API timeout (seconds)
DEYE_TIMEOUT=30

# ==========================================
# SCHEDULER CONFIGURATION
# ==========================================

# Enable/disable background data collection
SCHEDULER_ENABLED=true

# Data collection intervals (in seconds)
LIVE_DATA_INTERVAL=300          # 5 minutes
DAILY_DATA_HOUR=23              # 23:59 (11:59 PM)
MONTHLY_DATA_DAY=1              # 1st day of month

# Maximum retry attempts for failed API calls
MAX_API_RETRIES=3

# ==========================================
# SECURITY SETTINGS
# ==========================================

# Enable HTTPS redirect (production only)
FORCE_HTTPS=false

# CORS settings (for API access)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Session timeout (seconds)
SESSION_TIMEOUT=3600

# ==========================================
# MONITORING AND PERFORMANCE
# ==========================================

# Enable performance monitoring
MONITORING_ENABLED=false

# Prometheus metrics endpoint
METRICS_ENABLED=false

# Health check endpoint timeout
HEALTH_CHECK_TIMEOUT=5

# ==========================================
# EMAIL NOTIFICATIONS (Future feature)
# ==========================================

# SMTP server settings
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USE_SSL=false
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password

# Notification recipients
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# ==========================================
# SSL/TLS CONFIGURATION (Production)
# ==========================================

# SSL certificate files (for HTTPS)
SSL_CERTFILE=/path/to/certificate.pem
SSL_KEYFILE=/path/to/private_key.pem

# ==========================================
# DOCKER COMPOSE OVERRIDES
# ==========================================

# Docker Compose project name
COMPOSE_PROJECT_NAME=solar_dashboard

# Docker image tags
WEB_IMAGE_TAG=latest
DB_IMAGE_TAG=15-alpine
REDIS_IMAGE_TAG=7-alpine

# ==========================================
# BACKUP CONFIGURATION
# ==========================================

# Database backup settings
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/app/backups

# ==========================================
# DEVELOPMENT SETTINGS
# ==========================================

# Enable Flask debug toolbar
DEBUG_TOOLBAR_ENABLED=false

# Enable SQL query logging
SQL_DEBUG=false

# Auto-reload templates
TEMPLATES_AUTO_RELOAD=true

# ==========================================
# TESTING CONFIGURATION
# ==========================================

# Test database URL
TEST_DATABASE_URL=postgresql://postgres:test_password@localhost:5432/solar_dashboard_test

# Test API credentials (use test/demo account)
TEST_DEYE_USERNAME=test_user
TEST_DEYE_PASSWORD=test_password

# ==========================================
# EXAMPLE VALUES
# ==========================================

# Here are some example configurations:

# Development example:
# FLASK_ENV=development
# FLASK_DEBUG=1
# DATABASE_URL=postgresql://postgres:dev_password@localhost:5432/solar_dashboard_dev
# DEYE_USERNAME=your_actual_deye_username
# DEYE_PASSWORD=your_actual_deye_password

# Production example:
# FLASK_ENV=production
# FLASK_DEBUG=0
# SECRET_KEY=super_secure_random_key_here_use_a_key_generator
# DATABASE_URL=************************************************/solar_dashboard
# FORCE_HTTPS=true
# MONITORING_ENABLED=true
