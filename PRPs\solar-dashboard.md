name: "Solar Dashboard - Beautiful Animation-Rich Flask Interface with Deye Cloud API"
description: |

## Purpose
Template optimized for AI agents to implement a comprehensive solar dashboard with live Deye Cloud API data, intelligent scheduling, animated UI components, and robust data persistence through iterative refinement.

## Core Principles
1. **Context is King**: Include ALL necessary documentation, examples, and caveats
2. **Validation Loops**: Provide executable tests/lints the AI can run and fix
3. **Information Dense**: Use keywords and patterns from the codebase
4. **Progressive Success**: Start simple, validate, then enhance
5. **Global rules**: Be sure to follow all rules in .github/copilot-instructions.md

---

## Goal
Build a production-ready, beautiful, animation-rich solar dashboard interface using Flask that displays live data from Deye Cloud API with intelligent scheduling, animated cards for real-time statistics, accurate graphs for daily/monthly data visualization, custom date range selection, and comprehensive data storage for future processing capabilities.

## Why
- **Business value**: Provides real-time solar system monitoring and analytics
- **User impact**: Beautiful, responsive interface with live animations showing solar generation, battery levels, grid consumption
- **Integration**: Smart API usage with proper scheduling to avoid rate limits
- **Problems solved**: 
  - Real-time monitoring of solar inverter performance
  - Historical data analysis and visualization
  - Intelligent data collection without API abuse
  - Future-ready data storage for advanced analytics

## What
A Flask web application with:
- **Live animated cards** showing current PV generation, battery level, grid import, household consumption
- **Intelligent API scheduling**: Historical monthly (once/month), daily (once/day), live (5-min intervals or on-demand refresh)
- **Interactive graphs**: Daily line graphs (5-min intervals), monthly bar charts, custom date range selection
- **Comprehensive data storage**: All API responses stored in database for future processing
- **Docker deployment**: Complete containerization for easy development and deployment
- **Modern UI**: Beautiful animations and responsive design

### Success Criteria
- [ ] Flask application successfully authenticates with Deye Cloud API
- [ ] Intelligent scheduler collects data at appropriate intervals without breaking API limits
- [ ] Real-time animated cards display current solar system statistics
- [ ] Interactive graphs render daily and monthly data with Chart.js
- [ ] Custom date range selection works for historical data viewing
- [ ] All data is properly stored in database with SQLAlchemy models
- [ ] Docker container runs successfully with all components
- [ ] Playwright tests validate UI functionality and animations
- [ ] All pytest unit tests pass with proper coverage

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- url: https://flask.palletsprojects.com/en/stable/
  why: Core Flask framework patterns, routing, templating
  files: research/flask/page1.md, research/flask/page2.md
  
- url: https://flask.palletsprojects.com/en/stable/templating/
  why: Jinja2 templating for dynamic UI generation
  files: research/flask/page4.md
  
- url: https://flask.palletsprojects.com/en/stable/blueprints/
  why: Modular application structure with blueprints
  files: research/flask/page7.md
  
- url: https://flask.palletsprojects.com/en/stable/testing/
  why: Flask testing patterns and best practices
  files: research/flask/page8.md

- url: https://eu1-developer.deyecloud.com/v2/api-docs
  why: Complete Deye Cloud API specification - ALL endpoints, authentication, data models
  files: research/deye-cloud-api/page2.md
  critical: Token authentication, rate limits, regional endpoints, error codes
  
- url: https://developer.deyecloud.com/start/
  why: API quickstart guide and authentication flow
  files: research/deye-cloud-api/page3.md
  
- url: https://developer.deyecloud.com/support/openApi/faq
  why: Common API issues, best practices, limitations
  files: research/deye-cloud-api/page4.md

- file: examples/daily_deye_scraper.py
  why: EXISTING authentication pattern, API usage, data extraction
  critical: Shows exact token retrieval, station list, daily data patterns
  
- file: examples/obtain_device_latest.py
  why: Device latest data retrieval pattern
  
- file: examples/obtain_station_latest.py
  why: Station latest data retrieval pattern

- url: https://docs.sqlalchemy.org/en/20/orm/quickstart.html
  why: SQLAlchemy ORM patterns for data models and persistence
  files: research/sqlalchemy/page1.md
  
- url: https://apscheduler.readthedocs.io/en/3.x/
  why: Background task scheduling for intelligent API data collection
  files: research/apscheduler/page1.md
  
- url: https://www.chartjs.org/docs/latest/
  why: Chart.js for interactive graphs and data visualization
  files: research/chartjs/page1.md
  
- url: https://docs.docker.com/get-started/
  why: Docker containerization for development and deployment
  files: research/docker/page1.md
```

### Current Codebase tree
```bash
C:.
│   INITIAL.md
│   README.md
│   SETUP.md
│
├───examples
│       daily_deye_scraper.py      # CRITICAL: Existing API authentication pattern
│       obtain_device_latest.py
│       obtain_station_latest.py
│       obtain_station_history.py
│       obtain_device_history.py
│       # ... other API examples
│
├───PRPs
│   └───templates
│           prp_base.md
│
└───research
    ├───deye-cloud-api             # Complete API documentation
    ├───flask                      # Flask framework documentation
    ├───sqlalchemy                 # Database ORM documentation
    ├───apscheduler               # Task scheduling documentation
    ├───chartjs                   # Chart visualization documentation
    └───docker                    # Containerization documentation
```

### Desired Codebase tree with files to be added
```bash
C:.
├── app/
│   ├── __init__.py               # Flask app factory
│   ├── config.py                 # Configuration management
│   ├── models/
│   │   ├── __init__.py
│   │   ├── station.py           # Station data model
│   │   ├── device.py            # Device data model
│   │   └── measurement.py       # Measurement data model
│   ├── api/
│   │   ├── __init__.py
│   │   ├── deye_client.py       # Deye API client (based on daily_deye_scraper.py)
│   │   └── routes.py            # API endpoints for frontend
│   ├── scheduler/
│   │   ├── __init__.py
│   │   ├── tasks.py             # Background data collection tasks
│   │   └── scheduler.py         # APScheduler configuration
│   ├── static/
│   │   ├── css/
│   │   │   ├── main.css         # Main stylesheet with animations
│   │   │   └── dashboard.css    # Dashboard-specific styles
│   │   ├── js/
│   │   │   ├── dashboard.js     # Dashboard JavaScript
│   │   │   ├── charts.js        # Chart.js integration
│   │   │   └── animations.js    # UI animations
│   │   └── img/                 # Static images
│   └── templates/
│       ├── base.html            # Base template with common layout
│       ├── dashboard.html       # Main dashboard interface
│       └── components/
│           ├── live_cards.html  # Animated live data cards
│           └── charts.html      # Chart components
├── tests/
│   ├── __init__.py
│   ├── test_models.py           # Database model tests
│   ├── test_api_client.py       # Deye API client tests
│   ├── test_routes.py           # Flask route tests
│   ├── test_scheduler.py        # Scheduler task tests
│   └── conftest.py              # Pytest configuration
├── migrations/                   # Database migration files
├── docker/
│   ├── Dockerfile               # Main application container
│   ├── docker-compose.yml       # Multi-service setup
│   └── requirements.txt         # Python dependencies
├── .env.example                 # Environment variable template
├── .env                         # Environment variables (gitignored)
├── run.py                       # Application entry point
└── pytest.ini                  # Pytest configuration
```

### Known Gotchas of our codebase & Library Quirks
```python
# CRITICAL: Deye Cloud API Authentication
# Must use SHA256 encrypted password in lowercase
# Token expires after 60 days - need refresh mechanism
# Regional endpoints: EU (eu1-developer.deyecloud.com) vs US (us1-developer.deyecloud.com)

# CRITICAL: API Rate Limiting
# Historical monthly data: Only needed once per month
# Historical daily data: Only needed once per day (end of day)
# Live data: Every 5 minutes OR on user refresh click
# Batch operations limited (max 10 devices for latest data)

# CRITICAL: Flask Application Factory Pattern
# Use create_app() factory for proper testing and configuration
# Blueprints for modular route organization

# CRITICAL: SQLAlchemy with Flask
# Use Flask-SQLAlchemy for integration
# Database models must inherit from db.Model
# Use Flask-Migrate for database schema management

# CRITICAL: APScheduler with Flask
# Must initialize scheduler properly in app context
# Use BackgroundScheduler for production
# Store job state in database for persistence

# CRITICAL: Chart.js Integration
# Load Chart.js via CDN or npm install
# Use responsive: true for mobile compatibility
# Animation callbacks for smooth transitions

# CRITICAL: Docker Development
# Use multi-stage builds for production optimization
# Volume mount source code for development
# Use docker-compose for database + app services
```

## Implementation Blueprint

### Data models and structure

Create the core data models to ensure type safety and consistency:

```python
# app/models/station.py
from app import db
from datetime import datetime

class Station(db.Model):
    __tablename__ = 'stations'
    
    id = db.Column(db.Integer, primary_key=True)
    station_id = db.Column(db.String(50), unique=True, nullable=False)  # Deye station ID
    name = db.Column(db.String(200), nullable=False)
    location = db.Column(db.String(200))
    capacity = db.Column(db.Float)  # kW capacity
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    devices = db.relationship('Device', backref='station', lazy=True)
    measurements = db.relationship('StationMeasurement', backref='station', lazy=True)

# app/models/device.py  
class Device(db.Model):
    __tablename__ = 'devices'
    
    id = db.Column(db.Integer, primary_key=True)
    device_sn = db.Column(db.String(50), unique=True, nullable=False)  # Deye device serial
    device_type = db.Column(db.String(50), nullable=False)  # INVERTER, BATTERY, etc.
    station_id = db.Column(db.Integer, db.ForeignKey('stations.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    measurements = db.relationship('DeviceMeasurement', backref='device', lazy=True)

# app/models/measurement.py
class StationMeasurement(db.Model):
    __tablename__ = 'station_measurements'
    
    id = db.Column(db.Integer, primary_key=True)
    station_id = db.Column(db.Integer, db.ForeignKey('stations.id'), nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False)
    granularity = db.Column(db.String(20), nullable=False)  # 'live', 'daily', 'monthly'
    
    # Power values (kW)
    generation_power = db.Column(db.Float)
    consumption_power = db.Column(db.Float)
    grid_power = db.Column(db.Float)
    battery_power = db.Column(db.Float)
    battery_soc = db.Column(db.Float)  # State of charge %
    
    # Energy values (kWh)
    generation_value = db.Column(db.Float)
    consumption_value = db.Column(db.Float)
    grid_value = db.Column(db.Float)
    charge_value = db.Column(db.Float)
    discharge_value = db.Column(db.Float)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class DeviceMeasurement(db.Model):
    __tablename__ = 'device_measurements'
    
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id'), nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False)
    measure_point = db.Column(db.String(100), nullable=False)  # e.g., "SOC", "MI Voltage L2"
    value = db.Column(db.String(50), nullable=False)
    unit = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
```

### List of tasks to be completed to fulfill the PRP in order

```yaml
Task 1: Flask Application Setup
CREATE app/__init__.py:
  - PATTERN: Flask application factory (see research/flask/page6.md)
  - IMPLEMENT create_app() function with configuration
  - REGISTER blueprints for modular structure
  - CONFIGURE SQLAlchemy, APScheduler extensions

CREATE app/config.py:
  - MIRROR pattern from: examples/daily_deye_scraper.py (API credentials)
  - DEFINE configuration classes (Development, Production, Testing)
  - INCLUDE Deye API credentials, database URLs, scheduler settings

Task 2: Database Models Implementation
CREATE app/models/__init__.py:
  - IMPORT all model classes
  - SETUP Flask-SQLAlchemy db instance

CREATE app/models/station.py:
  - IMPLEMENT Station model (see pseudocode above)
  - INCLUDE relationships to devices and measurements

CREATE app/models/device.py:
  - IMPLEMENT Device model with station foreign key
  - FOLLOW Deye API device types (INVERTER, BATTERY, etc.)

CREATE app/models/measurement.py:
  - IMPLEMENT StationMeasurement and DeviceMeasurement models
  - SUPPORT all Deye API data points and granularities

Task 3: Deye API Client Implementation
CREATE app/api/deye_client.py:
  - MIRROR authentication pattern from: examples/daily_deye_scraper.py
  - IMPLEMENT token management with automatic refresh
  - CREATE methods for all required API calls:
    * get_token(app_id, app_secret, email, password)
    * get_station_list(token)
    * get_station_latest_data(token, station_id)
    * get_station_history_data(token, station_id, granularity, start_date, end_date)
    * get_device_latest_data(token, device_list)
    * get_device_history_data(token, device_sn, granularity, start_date, end_date, measure_points)
  - HANDLE API errors and rate limiting gracefully
  - FOLLOW research/deye-cloud-api/page2.md for complete API specification

Task 4: Background Scheduler Implementation
CREATE app/scheduler/scheduler.py:
  - PATTERN: APScheduler with Flask (see research/apscheduler/page1.md)
  - CONFIGURE BackgroundScheduler for production use
  - SETUP timezone-aware scheduling

CREATE app/scheduler/tasks.py:
  - IMPLEMENT intelligent data collection tasks:
    * collect_live_data(): Every 5 minutes for current power/SOC
    * collect_daily_data(): Once per day at 23:59 for previous day
    * collect_monthly_data(): Once per month for previous month
  - STORE all collected data in database models
  - HANDLE API failures with retry logic

Task 5: Flask API Routes
CREATE app/api/routes.py:
  - IMPLEMENT REST API endpoints for frontend:
    * GET /api/stations - List all stations
    * GET /api/stations/<id>/latest - Current station data
    * GET /api/stations/<id>/history - Historical data with date range
    * GET /api/devices/<sn>/latest - Current device data
    * POST /api/refresh - Manual data refresh trigger
  - RETURN JSON responses for Chart.js consumption
  - IMPLEMENT proper error handling and status codes

Task 6: Frontend Templates
CREATE app/templates/base.html:
  - PATTERN: Flask template inheritance (see research/flask/page4.md)
  - INCLUDE Chart.js, Bootstrap 5, custom CSS/JS
  - SETUP responsive mobile-first layout

CREATE app/templates/dashboard.html:
  - EXTEND base template
  - INCLUDE animated cards for live data display
  - EMBED chart containers for daily/monthly graphs
  - ADD date range picker for historical data

CREATE app/templates/components/live_cards.html:
  - IMPLEMENT animated cards showing:
    * Current PV Generation (with sun animation)
    * Battery Level (with charging animation)
    * Grid Import (with power flow animation)
    * Household Consumption (with usage indicator)
  - USE CSS animations for smooth transitions

Task 7: Frontend JavaScript
CREATE app/static/js/dashboard.js:
  - IMPLEMENT automatic data refresh every 5 minutes
  - HANDLE manual refresh button clicks
  - UPDATE live cards with smooth animations
  - MANAGE chart data updates

CREATE app/static/js/charts.js:
  - PATTERN: Chart.js integration (see research/chartjs/page1.md)
  - IMPLEMENT daily line chart (5-minute intervals)
  - IMPLEMENT monthly bar chart (daily totals)
  - ADD date range selection functionality
  - CONFIGURE responsive charts with animations

CREATE app/static/js/animations.js:
  - IMPLEMENT CSS animation helpers
  - CREATE smooth transitions for data updates
  - ADD loading spinners and progress indicators

Task 8: Styling and Animations
CREATE app/static/css/main.css:
  - IMPLEMENT responsive grid layout
  - DEFINE CSS animations for live cards
  - CREATE modern color scheme with solar theme
  - ADD mobile-responsive breakpoints

CREATE app/static/css/dashboard.css:
  - IMPLEMENT dashboard-specific styles
  - CREATE animated card components
  - STYLE chart containers and controls
  - ADD hover effects and transitions

Task 9: Docker Configuration
CREATE Dockerfile:
  - PATTERN: Multi-stage Docker build (see research/docker/page1.md)
  - INSTALL Python dependencies
  - COPY application code
  - EXPOSE Flask port (5000)
  - SET proper entrypoint

CREATE docker-compose.yml:
  - DEFINE Flask app service
  - ADD PostgreSQL database service
  - CONFIGURE environment variables
  - SETUP volume mounts for development

CREATE requirements.txt:
  - INCLUDE all Python dependencies:
    * Flask, Flask-SQLAlchemy, Flask-Migrate
    * APScheduler, requests, python-dotenv
    * pytest, pytest-flask for testing

Task 10: Application Entry Point
CREATE run.py:
  - IMPLEMENT application entry point
  - INITIALIZE Flask app with create_app()
  - START APScheduler for background tasks
  - CONFIGURE logging and error handling

Task 11: Environment Configuration
CREATE .env.example:
  - DEFINE all required environment variables
  - INCLUDE Deye API credentials template
  - ADD database configuration options

Task 12: Database Migrations
CREATE migrations/ directory:
  - INITIALIZE Flask-Migrate
  - CREATE initial database schema
  - SETUP migration management
```

### Per task pseudocode with CRITICAL details

```python
# Task 1: Flask Application Setup
# app/__init__.py
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from apscheduler.schedulers.background import BackgroundScheduler

db = SQLAlchemy()
migrate = Migrate()
scheduler = BackgroundScheduler()

def create_app(config_name='development'):
    """Application factory pattern for Flask"""
    app = Flask(__name__)
    
    # CRITICAL: Load configuration based on environment
    if config_name == 'development':
        app.config.from_object('app.config.DevelopmentConfig')
    elif config_name == 'production':
        app.config.from_object('app.config.ProductionConfig')
    
    # CRITICAL: Initialize extensions with app context
    db.init_app(app)
    migrate.init_app(app, db)
    
    # PATTERN: Register blueprints for modular structure
    from app.api.routes import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # CRITICAL: Start scheduler in app context
    with app.app_context():
        from app.scheduler.tasks import start_scheduled_tasks
        scheduler.start()
        start_scheduled_tasks()
    
    return app

# Task 3: Deye API Client Implementation
# app/api/deye_client.py
import requests
import hashlib
from datetime import datetime, timedelta
from app.config import Config

class DeyeAPIClient:
    """
    Deye Cloud API client based on examples/daily_deye_scraper.py pattern
    Handles authentication, token management, and all API calls
    """
    
    def __init__(self):
        self.base_url = "https://eu1-developer.deyecloud.com"  # EU region
        self.token = None
        self.token_expires = None
        
    def get_token(self):
        """
        PATTERN: Follow examples/daily_deye_scraper.py authentication
        CRITICAL: Password must be SHA256 encrypted in lowercase
        """
        if self.token and self.token_expires > datetime.utcnow():
            return self.token  # Return cached valid token
            
        url = f"{self.base_url}/v1.0/account/token?appId={Config.DEYE_APP_ID}"
        headers = {"Content-Type": "application/json"}
        
        # CRITICAL: SHA256 encryption as per examples/daily_deye_scraper.py
        hashed_password = hashlib.sha256(Config.DEYE_PASSWORD.encode('utf-8')).hexdigest().lower()
        
        data = {
            "appSecret": Config.DEYE_APP_SECRET,
            "email": Config.DEYE_EMAIL,
            "password": hashed_password,
        }
        
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        json_response = response.json()
        if json_response.get("success"):
            self.token = json_response.get("accessToken")
            # CRITICAL: Tokens expire after 60 days
            self.token_expires = datetime.utcnow() + timedelta(days=59)
            return self.token
        else:
            raise Exception(f"Token retrieval failed: {json_response.get('msg')}")
    
    def get_station_latest_data(self, station_id):
        """
        PATTERN: Follow research/deye-cloud-api/page2.md station real-time endpoint
        CRITICAL: Returns current power generation, consumption, battery status
        """
        token = self.get_token()
        url = f"{self.base_url}/v1.0/station/real-time-data"
        headers = {
            "Content-Type": "application/json",
            "authorization": f"Bearer {token}",
        }
        data = {"stationId": station_id}
        
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()

# Task 4: Background Scheduler Implementation
# app/scheduler/tasks.py
from apscheduler.triggers.cron import CronTrigger
from app import scheduler, db
from app.api.deye_client import DeyeAPIClient
from app.models.measurement import StationMeasurement

def collect_live_data():
    """
    CRITICAL: Collect live data every 5 minutes
    PATTERN: Store all data in database for future processing
    """
    client = DeyeAPIClient()
    stations = Station.query.all()
    
    for station in stations:
        try:
            # CRITICAL: API call for real-time data
            data = client.get_station_latest_data(station.station_id)
            
            if data.get("success"):
                station_data = data.get("data", {})
                
                # CRITICAL: Store in database with 'live' granularity
                measurement = StationMeasurement(
                    station_id=station.id,
                    timestamp=datetime.utcnow(),
                    granularity='live',
                    generation_power=station_data.get('generationPower'),
                    consumption_power=station_data.get('consumptionPower'),
                    grid_power=station_data.get('gridPower'),
                    battery_power=station_data.get('batteryPower'),
                    battery_soc=station_data.get('batterySOC'),
                )
                db.session.add(measurement)
                
        except Exception as e:
            # GOTCHA: Log errors but continue with other stations
            app.logger.error(f"Failed to collect live data for station {station.station_id}: {e}")
    
    db.session.commit()

def start_scheduled_tasks():
    """Initialize all background tasks with proper scheduling"""
    # CRITICAL: Live data every 5 minutes
    scheduler.add_job(
        func=collect_live_data,
        trigger=CronTrigger(minute='*/5'),  # Every 5 minutes
        id='collect_live_data',
        replace_existing=True
    )
    
    # CRITICAL: Daily data once per day at 23:59
    scheduler.add_job(
        func=collect_daily_data,
        trigger=CronTrigger(hour=23, minute=59),  # End of day
        id='collect_daily_data',
        replace_existing=True
    )

# Task 6: Frontend Templates
# app/templates/dashboard.html
{% extends "base.html" %}
{% block title %}Solar Dashboard{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- CRITICAL: Animated live data cards -->
    <div class="live-cards-grid">
        <div class="card live-card" id="pv-generation-card">
            <div class="card-icon">☀️</div>
            <div class="card-content">
                <h3>PV Generation</h3>
                <div class="value" id="pv-generation">0.0 kW</div>
                <div class="animation-bar" id="pv-bar"></div>
            </div>
        </div>
        
        <div class="card live-card" id="battery-level-card">
            <div class="card-icon">🔋</div>
            <div class="card-content">
                <h3>Battery Level</h3>
                <div class="value" id="battery-soc">0%</div>
                <div class="battery-animation" id="battery-animation"></div>
            </div>
        </div>
        
        <!-- Similar cards for Grid Import and Household Consumption -->
    </div>
    
    <!-- CRITICAL: Chart containers for daily/monthly graphs -->
    <div class="charts-section">
        <div class="chart-controls">
            <button id="daily-chart-btn" class="chart-btn active">Daily</button>
            <button id="monthly-chart-btn" class="chart-btn">Monthly</button>
            <input type="date" id="date-picker" class="date-picker">
        </div>
        
        <canvas id="main-chart" width="400" height="200"></canvas>
    </div>
</div>
{% endblock %}

# Task 7: Frontend JavaScript
# app/static/js/dashboard.js
class SolarDashboard {
    constructor() {
        this.refreshInterval = 5 * 60 * 1000; // 5 minutes in milliseconds
        this.chart = null;
        this.currentView = 'daily';
        
        this.initializeEventListeners();
        this.startAutoRefresh();
        this.loadInitialData();
    }
    
    async loadLiveData() {
        """
        CRITICAL: Fetch live data from Flask API
        PATTERN: Update animated cards with smooth transitions
        """
        try {
            const response = await fetch('/api/stations/1/latest');
            const data = await response.json();
            
            if (data.success) {
                // CRITICAL: Update cards with animations
                this.updateLiveCard('pv-generation', data.generationPower, 'kW');
                this.updateLiveCard('battery-soc', data.batterySOC, '%');
                this.updateLiveCard('grid-power', data.gridPower, 'kW');
                this.updateLiveCard('consumption-power', data.consumptionPower, 'kW');
                
                // PATTERN: Trigger CSS animations
                this.animateValueUpdate();
            }
        } catch (error) {
            console.error('Failed to load live data:', error);
        }
    }
    
    updateLiveCard(elementId, value, unit) {
        """CRITICAL: Smooth animation when updating card values"""
        const element = document.getElementById(elementId);
        element.classList.add('updating');
        
        setTimeout(() => {
            element.textContent = `${value} ${unit}`;
            element.classList.remove('updating');
            element.classList.add('updated');
        }, 200);
    }
}

// CRITICAL: Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new SolarDashboard();
});
```

### Integration Points
```yaml
DATABASE:
  - migration: "Create initial schema with Station, Device, Measurement tables"
  - indexes: "CREATE INDEX idx_measurements_timestamp ON station_measurements(timestamp)"
  - relationships: "Foreign keys between stations, devices, and measurements"
  
CONFIG:
  - add to: app/config.py
  - pattern: "DEYE_APP_ID = os.getenv('DEYE_APP_ID')"
  - include: API credentials, database URLs, scheduler settings
  
ROUTES:
  - add to: app/api/routes.py  
  - pattern: "blueprint.route('/stations/<int:id>/latest', methods=['GET'])"
  - implement: RESTful API for frontend data consumption

SCHEDULER:
  - add to: app/scheduler/tasks.py
  - pattern: "scheduler.add_job(func=collect_live_data, trigger=CronTrigger(minute='*/5'))"
  - critical: Intelligent scheduling to avoid API rate limits

TEMPLATES:
  - add to: app/templates/
  - pattern: "{% extends 'base.html' %} {% block content %}"
  - include: Animated cards, Chart.js integration, responsive design

DOCKER:
  - add to: Dockerfile, docker-compose.yml
  - pattern: "FROM python:3.11-slim"
  - include: Multi-service setup with PostgreSQL
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
cd "C:\Users\<USER>\Documents\Coding\New Solar Display"

# Install dependencies in Docker container
docker-compose build
docker-compose up -d

# Check Python syntax and style
docker-compose exec app python -m flake8 app/ --max-line-length=120
docker-compose exec app python -m mypy app/ --ignore-missing-imports

# Expected: No errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests
```python
# CREATE tests/test_deye_client.py with these test cases:
def test_token_authentication():
    """Test Deye API token retrieval"""
    client = DeyeAPIClient()
    token = client.get_token()
    assert token is not None
    assert len(token) > 0

def test_station_latest_data():
    """Test station latest data retrieval"""
    client = DeyeAPIClient()
    data = client.get_station_latest_data(station_id=1)
    assert data['success'] is True
    assert 'generationPower' in data.get('data', {})

def test_database_models():
    """Test database model creation and relationships"""
    station = Station(station_id="TEST_001", name="Test Station")
    db.session.add(station)
    db.session.commit()
    
    assert station.id is not None
    assert station.station_id == "TEST_001"

def test_scheduler_tasks():
    """Test background task execution"""
    with app.app_context():
        # Mock API response
        with mock.patch('app.api.deye_client.DeyeAPIClient.get_station_latest_data') as mock_api:
            mock_api.return_value = {
                'success': True,
                'data': {'generationPower': 5.2, 'batterySOC': 85.5}
            }
            
            collect_live_data()
            
            # Verify data was stored
            measurement = StationMeasurement.query.filter_by(granularity='live').first()
            assert measurement is not None
            assert measurement.generation_power == 5.2
```

```bash
# Run and iterate until passing:
docker-compose exec app python -m pytest tests/ -v --tb=short

# If failing: Read error, understand root cause, fix code, re-run
# NEVER mock to pass - fix actual implementation
```

### Level 3: Integration Test
```bash
# Start the complete service stack
docker-compose up -d

# Wait for services to be ready
docker-compose exec app python -c "
import time
from app import create_app, db
app = create_app()
with app.app_context():
    # Wait for database
    for i in range(30):
        try:
            db.engine.execute('SELECT 1')
            print('Database ready')
            break
        except:
            time.sleep(1)
    else:
        raise Exception('Database not ready')
"

# Test the dashboard endpoint
curl -X GET http://localhost:5000/ \
  -H "Accept: text/html"
# Expected: HTML dashboard page with animated cards

# Test API endpoints
curl -X GET http://localhost:5000/api/stations \
  -H "Content-Type: application/json"
# Expected: {"success": true, "stations": [...]}

# Test live data endpoint
curl -X GET http://localhost:5000/api/stations/1/latest \
  -H "Content-Type: application/json"
# Expected: {"success": true, "data": {"generationPower": X.X, ...}}

# Test manual refresh trigger
curl -X POST http://localhost:5000/api/refresh \
  -H "Content-Type: application/json"
# Expected: {"success": true, "message": "Data refresh triggered"}

# Check Docker logs for any errors
docker-compose logs app
# Expected: No error messages, successful API calls logged

# Test Playwright UI automation
docker-compose exec app python -m pytest tests/test_ui.py -v
# Expected: UI tests pass, animations working, charts rendering
```

### Level 4: Playwright UI Validation
```python
# CREATE tests/test_ui.py for UI testing
import pytest
from playwright.sync_api import Page, expect

def test_dashboard_loads(page: Page):
    """Test dashboard page loads with all components"""
    page.goto("http://localhost:5000")
    
    # Verify animated cards are present
    expect(page.locator("#pv-generation-card")).to_be_visible()
    expect(page.locator("#battery-level-card")).to_be_visible()
    
    # Verify chart container exists
    expect(page.locator("#main-chart")).to_be_visible()

def test_live_data_updates(page: Page):
    """Test live data cards update with animations"""
    page.goto("http://localhost:5000")
    
    # Get initial value
    initial_value = page.locator("#pv-generation").text_content()
    
    # Trigger manual refresh
    page.click("#refresh-btn")
    
    # Wait for animation and verify update
    page.wait_for_timeout(2000)  # Wait for animation
    updated_value = page.locator("#pv-generation").text_content()
    
    # Values should update (even if same, timestamp changes)
    expect(page.locator("#pv-generation")).to_have_class(/updated/)

def test_chart_interactions(page: Page):
    """Test chart view switching and date selection"""
    page.goto("http://localhost:5000")
    
    # Test monthly chart switch
    page.click("#monthly-chart-btn")
    expect(page.locator("#monthly-chart-btn")).to_have_class(/active/)
    
    # Test date picker
    page.fill("#date-picker", "2024-01-15")
    page.wait_for_timeout(1000)  # Wait for chart update
    
    # Chart should update with new data
    expect(page.locator("#main-chart")).to_be_visible()
```

## Final validation Checklist
- [ ] All tests pass: `docker-compose exec app python -m pytest tests/ -v`
- [ ] No linting errors: `docker-compose exec app python -m flake8 app/`
- [ ] No type errors: `docker-compose exec app python -m mypy app/`
- [ ] Dashboard loads successfully: `curl http://localhost:5000/`
- [ ] API endpoints respond correctly: `curl http://localhost:5000/api/stations`
- [ ] Live data updates every 5 minutes (check logs)
- [ ] Charts render with Chart.js integration
- [ ] Animations work smoothly in Playwright tests
- [ ] Docker containers start without errors
- [ ] Database migrations applied successfully
- [ ] Deye API authentication working
- [ ] Background scheduler running tasks
- [ ] Error cases handled gracefully (network failures, API errors)

## Confidence Score: 9/10

This PRP provides comprehensive context including:
- ✅ Complete Deye Cloud API documentation with examples
- ✅ Existing authentication patterns from daily_deye_scraper.py
- ✅ Flask best practices and patterns
- ✅ Detailed database models and relationships
- ✅ Smart scheduling strategy to avoid API limits
- ✅ Modern UI with animations and Chart.js integration
- ✅ Docker containerization for reliable deployment
- ✅ Comprehensive testing strategy with Playwright
- ✅ All critical gotchas and library quirks documented
- ✅ Step-by-step implementation blueprint

The only potential challenge is the complexity of coordinating all components, but the modular approach and comprehensive testing should enable successful one-pass implementation.
