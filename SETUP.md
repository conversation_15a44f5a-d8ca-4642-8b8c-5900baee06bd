# Project Setup

## Quick Start

1. **Clone the repository:**
   ```bash
   git clone https://github.com/IncomeStreamSurfer/context-engineering-intro.git
   cd context-engineering-intro
   ```

2. **Enable Copilot context awareness:**
   GitHub Copilot does not use user-side hooks, but you can provide context by editing `COPILOT.md` and using the `/research/` directory for documentation. No manual hook setup is required.

3. **Start using GitHub Copilot:**
   Ensure the GitHub Copilot extension is enabled in your editor. You can now use Copilot to generate and edit code with the provided context.

## How The Hooks Work

When you write or edit code that uses external APIs, the documentation hook will:

1. **Extract keywords** from your code (imports, API calls, function names)
2. **Search** the `research/` directory for relevant documentation
3. **Show relevant docs** to Copilot before writing code
4. **Ensure accuracy** by providing up-to-date API documentation

### Example

When you write:
```python
from openai import OpenAI
client = OpenAI()
```

You should manually review the relevant OpenAI documentation from `research/openai/` before proceeding, ensuring accurate implementation with Co<PERSON><PERSON>.

## Research Directory

The `research/` folder contains up-to-date documentation for:
- OpenAI API
- Pydantic AI
- GitHub Copilot
- Jina AI
- And more...

## Project Structure

```
├── .copilot/                # Copilot configuration (if needed)
│   ├── hooks/               # Documentation hooks
│   └── settings.json        # Hook configuration
├── research/                # API documentation
├── examples/                # Code examples
├── phase-1.md              # Project phase 1 specs
├── phase-2.md              # Project phase 2 specs
└── copilot-instructions.md  # AI coding instructions
```

## Benefits

- **Accurate implementations** - Always uses latest documentation
- **Faster development** - No need to manually look up APIs
- **Consistent patterns** - Follows documented best practices
- **Reduced errors** - Prevents using outdated API patterns

The hooks ensure that Claude always has the most current and accurate documentation when implementing features, leading to better code quality and fewer API-related bugs.