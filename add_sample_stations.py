#!/usr/bin/env python3
"""
Script to add sample stations to the database for testing the dashboard
"""

from app import create_app, db
from app.models import Station
import json

def add_sample_stations():
    """Add sample stations to test the dashboard"""
    app = create_app()
    with app.app_context():
        # Check if stations already exist
        existing_stations = Station.query.count()
        if existing_stations > 0:
            print(f"Found {existing_stations} existing stations. Skipping creation.")
            return
        
        # Create sample stations
        station1 = Station(
            name='Home Solar Array',
            location='Main Residence',
            capacity=10.5,
            deye_station_id='STA123456',
            api_key_encrypted='sample_encrypted_key_1',
            configuration={'timezone': 'UTC', 'panels': 24}
        )
        
        station2 = Station(
            name='Garage Solar Setup', 
            location='Garage Building',
            capacity=5.2,
            deye_station_id='STA789012',
            api_key_encrypted='sample_encrypted_key_2',
            configuration={'timezone': 'UTC', 'panels': 12}
        )
        
        station3 = Station(
            name='Workshop Solar',
            location='Workshop Building', 
            capacity=3.8,
            deye_station_id='STA345678',
            api_key_encrypted='sample_encrypted_key_3',
            configuration={'timezone': 'UTC', 'panels': 8}
        )
        
        # Add to database
        db.session.add(station1)
        db.session.add(station2) 
        db.session.add(station3)
        db.session.commit()
        
        print('Sample stations added successfully!')
        print(f'- {station1.name} ({station1.capacity} kW)')
        print(f'- {station2.name} ({station2.capacity} kW)')
        print(f'- {station3.name} ({station3.capacity} kW)')

if __name__ == '__main__':
    add_sample_stations()
