"""
Flask application factory for Solar Dashboard.
Based on Flask best practices and PRP requirements.
"""

from flask import Flask, render_template
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from apscheduler.schedulers.background import BackgroundScheduler
import atexit
import os

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
scheduler = BackgroundScheduler()

def create_app(config_name=None):
    """
    Application factory pattern for Flask.
    PATTERN: Based on research/flask/page6.md
    """
    app = Flask(__name__)
    
    # CRITICAL: Load configuration based on environment
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')
    
    if config_name == 'development':
        app.config.from_object('app.config.DevelopmentConfig')
    elif config_name == 'production':
        app.config.from_object('app.config.ProductionConfig')
    elif config_name == 'testing':
        app.config.from_object('app.config.TestingConfig')
    else:
        app.config.from_object('app.config.DevelopmentConfig')
    
    # CRITICAL: Initialize extensions with app context
    db.init_app(app)
    migrate.init_app(app, db)
    
    # Import models to ensure they're registered with SQLAlchemy
    from app.models import station, device, measurement
    
    # PATTERN: Register blueprints for modular structure
    from app.api.routes import api_bp
    from app.main.routes import main_bp
    
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(main_bp)
    
    # CRITICAL: Start scheduler in app context only in production/development
    if config_name in ['development', 'production']:
        with app.app_context():
            from app.scheduler.tasks import start_scheduled_tasks
            if not scheduler.running:
                scheduler.start()
                start_scheduled_tasks()
                # Shut down scheduler when exiting the app
                atexit.register(lambda: scheduler.shutdown())
    
    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    return app
