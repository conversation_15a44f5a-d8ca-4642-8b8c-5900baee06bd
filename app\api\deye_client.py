"""
Deye Cloud API client implementation.
PATTERN: Based on examples/daily_deye_scraper.py authentication pattern.
CRITICAL: Handles token management, rate limiting, and error handling.
"""

import requests
import hashlib
import time
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, List, Any
import logging

from app.config import Config

logger = logging.getLogger(__name__)


class DeyeAPIError(Exception):
    """Custom exception for Deye API errors."""
    pass


class DeyeAPIClient:
    """
    Deye Cloud API client with automatic token management.
    PATTERN: Follows examples/daily_deye_scraper.py authentication flow.
    CRITICAL: Implements intelligent rate limiting and error handling.
    """
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize the API client with configuration."""
        self.config = config or Config()
        self.base_url = self.config.DEYE_BASE_URL
        self.app_id = self.config.DEYE_APP_ID
        self.app_secret = self.config.DEYE_APP_SECRET
        self.email = self.config.DEYE_EMAIL
        self.password = self.config.DEYE_PASSWORD
        
        # Token management
        self.token = None
        self.token_expires = None
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum 1 second between requests
        
        # Request session for connection pooling
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Solar-Dashboard/1.0'
        })
    
    def _wait_for_rate_limit(self):
        """Ensure we don't exceed API rate limits."""
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()
    
    def get_token(self) -> str:
        """
        Retrieve access token from Deye API.
        PATTERN: Exact implementation from examples/daily_deye_scraper.py
        CRITICAL: Password must be SHA256 encrypted in lowercase.
        """
        # Return cached token if still valid
        if self.token and self.token_expires and self.token_expires > datetime.utcnow():
            return self.token
        
        logger.info("Requesting new access token from Deye API")
        
        url = f"{self.base_url}/v1.0/account/token?appId={self.app_id}"
        
        # CRITICAL: SHA256 encryption as per examples/daily_deye_scraper.py
        hashed_password = hashlib.sha256(self.password.encode('utf-8')).hexdigest().lower()
        
        data = {
            "appSecret": self.app_secret,
            "email": self.email,
            "password": hashed_password,
        }
        
        try:
            self._wait_for_rate_limit()
            response = self.session.post(url, json=data, timeout=30)
            response.raise_for_status()
            
            json_response = response.json()
            
            if json_response.get("success"):
                self.token = json_response.get("accessToken")
                # CRITICAL: Tokens expire after 60 days according to research
                self.token_expires = datetime.utcnow() + timedelta(days=59)
                logger.info("Successfully obtained access token")
                return self.token
            else:
                error_msg = json_response.get('msg', 'Unknown error')
                logger.error(f"Token retrieval failed: {error_msg}")
                raise DeyeAPIError(f"Token retrieval failed: {error_msg}")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Network error during token retrieval: {e}")
            raise DeyeAPIError(f"Network error during token retrieval: {e}")
    
    def _make_authenticated_request(self, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Make an authenticated request to the Deye API.
        CRITICAL: Includes automatic token refresh and error handling.
        """
        token = self.get_token()
        url = f"{self.base_url}{endpoint}"
        
        headers = {
            "authorization": f"Bearer {token}",
        }
        
        try:
            self._wait_for_rate_limit()
            response = self.session.post(url, headers=headers, json=data or {}, timeout=30)
            response.raise_for_status()
            
            json_response = response.json()
            
            if not json_response.get("success"):
                error_msg = json_response.get('msg', 'API request failed')
                logger.warning(f"API request failed: {error_msg}")
                raise DeyeAPIError(f"API request failed: {error_msg}")
            
            return json_response
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Network error during API request to {endpoint}: {e}")
            raise DeyeAPIError(f"Network error during API request: {e}")
    
    def get_station_list(self) -> List[Dict[str, Any]]:
        """
        Retrieve list of stations associated with the account.
        PATTERN: Based on examples/daily_deye_scraper.py get_station_list function.
        """
        logger.info("Fetching station list from Deye API")
        
        data = {"page": 1, "size": 200}  # Get up to 200 stations
        
        response = self._make_authenticated_request("/v1.0/station/list", data)
        
        stations = response.get("stationList", [])
        logger.info(f"Retrieved {len(stations)} stations")
        
        return stations
    
    def get_station_latest_data(self, station_id: int) -> Dict[str, Any]:
        """
        Retrieve real-time data for a specific station.
        CRITICAL: Returns current power generation, consumption, battery status.
        """
        logger.debug(f"Fetching latest data for station {station_id}")
        
        data = {"stationId": station_id}
        
        response = self._make_authenticated_request("/v1.0/station/real-time-data", data)
        
        return response.get("data", {})
    
    def get_station_history_data(self, station_id: int, granularity: int, 
                                start_date: str, end_date: str = None) -> List[Dict[str, Any]]:
        """
        Retrieve historical data for a station.
        CRITICAL: Granularity determines data type:
        - granularity=2: Daily statistics (format: 'yyyy-MM-dd', up to 31 days)
        - granularity=3: Monthly statistics (format: 'yyyy-MM', up to 12 months)
        """
        logger.debug(f"Fetching history data for station {station_id}, granularity {granularity}")
        
        data = {
            "stationId": station_id,
            "granularity": granularity,
            "startAt": start_date,
        }
        
        # End date required for granularity 2 and 3
        if granularity in [2, 3] and end_date:
            data["endAt"] = end_date
        
        response = self._make_authenticated_request("/v1.0/station/history", data)
        
        return response.get("stationDataItems", [])
    
    def get_device_latest_data(self, device_list: List[str]) -> List[Dict[str, Any]]:
        """
        Retrieve latest data for multiple devices.
        CRITICAL: Supports batch queries, up to 10 devices per batch.
        """
        if len(device_list) > 10:
            logger.warning(f"Device list contains {len(device_list)} devices, limiting to 10")
            device_list = device_list[:10]
        
        logger.debug(f"Fetching latest data for {len(device_list)} devices")
        
        data = {"deviceList": device_list}
        
        response = self._make_authenticated_request("/v1.0/device/latest", data)
        
        return response.get("deviceDataList", [])
    
    def get_device_measure_points(self, device_sn: str) -> List[Dict[str, Any]]:
        """
        Retrieve available measure points for a device.
        CRITICAL: Required for device history data queries with granularity=1.
        """
        logger.debug(f"Fetching measure points for device {device_sn}")
        
        data = {"deviceSn": device_sn}
        
        response = self._make_authenticated_request("/v1.0/device/measurePoints", data)
        
        return response.get("measurePoints", [])
    
    def get_device_history_data(self, device_sn: str, granularity: int,
                               start_date: str, end_date: str = None,
                               measure_points: List[str] = None) -> List[Dict[str, Any]]:
        """
        Retrieve historical data for a device.
        CRITICAL: Different granularities require different parameters:
        - granularity=1: Daily data with measure points (requires measure_points)
        - granularity=2: Daily statistics
        - granularity=3: Monthly statistics
        """
        logger.debug(f"Fetching history data for device {device_sn}, granularity {granularity}")
        
        data = {
            "deviceSn": device_sn,
            "granularity": granularity,
            "startAt": start_date,
        }
        
        if granularity == 1 and measure_points:
            data["measurePoints"] = measure_points
        elif granularity in [2, 3] and end_date:
            data["endAt"] = end_date
        
        response = self._make_authenticated_request("/v1.0/device/history", data)
        
        return response.get("deviceDataItems", [])
    
    def close(self):
        """Clean up the session."""
        if self.session:
            self.session.close()
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
