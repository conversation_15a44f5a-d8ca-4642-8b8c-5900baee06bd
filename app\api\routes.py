"""
API routes for frontend data consumption.
PATTERN: RESTful API endpoints for Chart.js and dashboard integration.
CRITICAL: Returns JSON responses optimized for frontend consumption.
"""

import logging
from datetime import datetime, timedelta
from flask import Blueprint, jsonify, request, current_app
from sqlalchemy import desc, and_

from app import db
from app.models.station import Station
from app.models.device import Device
from app.models.measurement import StationMeasurement, DeviceMeasurement
from app.scheduler.tasks import manual_data_refresh

logger = logging.getLogger(__name__)

# Create API blueprint
api_bp = Blueprint('api', __name__)


@api_bp.route('/stations', methods=['GET'])
def get_stations():
    """
    Get list of all stations.
    Returns station information for dashboard initialization.
    """
    try:
        stations = Station.query.filter_by(is_active=True).all()
        
        stations_data = []
        for station in stations:
            station_dict = station.to_dict()
            
            # Add latest measurement info
            latest_measurement = station.get_latest_measurement()
            if latest_measurement:
                station_dict['latest_measurement'] = latest_measurement.to_dict()
            
            # Add device count
            station_dict['device_count'] = station.devices.filter_by(is_active=True).count()
            
            stations_data.append(station_dict)
        
        return jsonify({
            'success': True,
            'stations': stations_data,
            'count': len(stations_data)
        })
        
    except Exception as e:
        logger.error(f"Error fetching stations: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch stations'
        }), 500


@api_bp.route('/stations/<int:station_id>/latest', methods=['GET'])
def get_station_latest(station_id):
    """
    Get latest real-time data for a specific station.
    CRITICAL: Returns current power values for animated cards.
    """
    try:
        station = Station.query.get_or_404(station_id)
        
        # Get the most recent live measurement
        latest_measurement = station.measurements.filter_by(
            granularity='live'
        ).order_by(desc(StationMeasurement.timestamp)).first()
        
        if not latest_measurement:
            return jsonify({
                'success': False,
                'error': 'No live data available for this station'
            }), 404
        
        # Prepare response optimized for frontend
        response_data = {
            'station_id': station.id,
            'station_name': station.name,
            'timestamp': latest_measurement.timestamp.isoformat(),
            'data': {
                'generationPower': latest_measurement.generation_power or 0,
                'consumptionPower': latest_measurement.consumption_power or 0,
                'gridPower': latest_measurement.grid_power or 0,
                'batteryPower': latest_measurement.battery_power or 0,
                'batterySOC': latest_measurement.battery_soc or 0,
            },
            'age_minutes': (datetime.utcnow() - latest_measurement.timestamp).total_seconds() / 60
        }
        
        return jsonify({
            'success': True,
            **response_data
        })
        
    except Exception as e:
        logger.error(f"Error fetching latest data for station {station_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch latest station data'
        }), 500


@api_bp.route('/stations/<int:station_id>/history', methods=['GET'])
def get_station_history(station_id):
    """
    Get historical data for a station with date range support.
    CRITICAL: Returns data formatted for Chart.js consumption.
    """
    try:
        station = Station.query.get_or_404(station_id)
        
        # Parse query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        granularity = request.args.get('granularity', 'live')
        
        # Default date range if not provided
        if not start_date:
            if granularity == 'daily':
                start_date = (datetime.utcnow() - timedelta(days=30)).date()
            elif granularity == 'monthly':
                start_date = (datetime.utcnow() - timedelta(days=365)).date()
            else:  # live
                start_date = (datetime.utcnow() - timedelta(days=1)).date()
        else:
            start_date = datetime.fromisoformat(start_date).date()
        
        if not end_date:
            end_date = datetime.utcnow().date()
        else:
            end_date = datetime.fromisoformat(end_date).date()
        
        # Query measurements
        measurements = station.measurements.filter(
            and_(
                StationMeasurement.timestamp >= datetime.combine(start_date, datetime.min.time()),
                StationMeasurement.timestamp <= datetime.combine(end_date, datetime.max.time()),
                StationMeasurement.granularity == granularity
            )
        ).order_by(StationMeasurement.timestamp.asc()).all()
        
        # Format data for Chart.js
        chart_data = {
            'labels': [],
            'datasets': {
                'generation': [],
                'consumption': [],
                'grid': [],
                'battery_soc': []
            }
        }
        
        for measurement in measurements:
            # Format timestamp based on granularity
            if granularity == 'live':
                label = measurement.timestamp.strftime('%H:%M')
            elif granularity == 'daily':
                label = measurement.timestamp.strftime('%Y-%m-%d')
            else:  # monthly
                label = measurement.timestamp.strftime('%Y-%m')
            
            chart_data['labels'].append(label)
            
            # Use appropriate data fields based on granularity
            if granularity == 'live':
                chart_data['datasets']['generation'].append(measurement.generation_power or 0)
                chart_data['datasets']['consumption'].append(measurement.consumption_power or 0)
                chart_data['datasets']['grid'].append(measurement.grid_power or 0)
            else:
                chart_data['datasets']['generation'].append(measurement.generation_value or 0)
                chart_data['datasets']['consumption'].append(measurement.consumption_value or 0)
                chart_data['datasets']['grid'].append(measurement.grid_value or 0)
            
            chart_data['datasets']['battery_soc'].append(measurement.battery_soc or 0)
        
        return jsonify({
            'success': True,
            'station_id': station.id,
            'station_name': station.name,
            'granularity': granularity,
            'date_range': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'chart_data': chart_data,
            'data_points': len(measurements)
        })
        
    except Exception as e:
        logger.error(f"Error fetching history data for station {station_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch historical data'
        }), 500


@api_bp.route('/devices/<device_sn>/latest', methods=['GET'])
def get_device_latest(device_sn):
    """
    Get latest data for a specific device.
    Returns detailed device measurements.
    """
    try:
        device = Device.query.filter_by(device_sn=device_sn).first_or_404()
        
        # Get recent measurements
        measurements = device.measurements.order_by(
            desc(DeviceMeasurement.timestamp)
        ).limit(20).all()
        
        # Group measurements by measure point
        measure_points = {}
        for measurement in measurements:
            if measurement.measure_point not in measure_points:
                measure_points[measurement.measure_point] = []
            measure_points[measurement.measure_point].append(measurement.to_dict())
        
        device_data = device.to_dict()
        device_data['measurements'] = measure_points
        
        return jsonify({
            'success': True,
            'device': device_data
        })
        
    except Exception as e:
        logger.error(f"Error fetching device data for {device_sn}: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch device data'
        }), 500


@api_bp.route('/refresh', methods=['POST'])
def trigger_refresh():
    """
    Manually trigger data refresh.
    CRITICAL: Immediate data collection for dashboard refresh button.
    """
    try:
        logger.info("Manual refresh triggered via API")
        
        # Trigger manual data refresh
        success = manual_data_refresh()
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Data refresh triggered successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Data refresh failed'
            }), 500
            
    except Exception as e:
        logger.error(f"Error triggering manual refresh: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to trigger data refresh'
        }), 500


@api_bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint for monitoring.
    Returns system status and recent data availability.
    """
    try:
        # Check database connectivity
        station_count = Station.query.count()
        
        # Check recent data availability
        recent_measurement = StationMeasurement.query.filter_by(
            granularity='live'
        ).order_by(desc(StationMeasurement.timestamp)).first()
        
        health_data = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'database': {
                'connected': True,
                'station_count': station_count
            },
            'data': {
                'last_measurement': recent_measurement.timestamp.isoformat() if recent_measurement else None,
                'last_measurement_age_minutes': (
                    (datetime.utcnow() - recent_measurement.timestamp).total_seconds() / 60
                ) if recent_measurement else None
            }
        }
        
        return jsonify({
            'success': True,
            **health_data
        })
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'success': False,
            'status': 'unhealthy',
            'error': str(e)
        }), 500


@api_bp.route('/stats', methods=['GET'])
def get_system_stats():
    """
    Get system statistics for dashboard overview.
    Returns aggregate data across all stations.
    """
    try:
        # Get all active stations
        stations = Station.query.filter_by(is_active=True).all()
        
        total_capacity = sum(s.capacity or 0 for s in stations)
        total_generation = 0
        total_consumption = 0
        total_battery_capacity = 0
        average_battery_soc = 0
        
        # Aggregate latest measurements
        soc_values = []
        for station in stations:
            latest = station.get_latest_measurement()
            if latest:
                total_generation += latest.generation_power or 0
                total_consumption += latest.consumption_power or 0
                if latest.battery_soc is not None:
                    soc_values.append(latest.battery_soc)
        
        if soc_values:
            average_battery_soc = sum(soc_values) / len(soc_values)
        
        # Calculate efficiency
        efficiency = (total_generation / total_capacity * 100) if total_capacity > 0 else 0
        
        stats = {
            'overview': {
                'total_stations': len(stations),
                'total_capacity_kw': total_capacity,
                'current_generation_kw': total_generation,
                'current_consumption_kw': total_consumption,
                'average_battery_soc': round(average_battery_soc, 1),
                'system_efficiency': round(efficiency, 1)
            },
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error fetching system stats: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch system statistics'
        }), 500


# Error handlers for API blueprint
@api_bp.errorhandler(404)
def api_not_found(error):
    return jsonify({
        'success': False,
        'error': 'Resource not found'
    }), 404


@api_bp.errorhandler(500)
def api_internal_error(error):
    db.session.rollback()
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500
