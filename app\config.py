"""
Configuration classes for the Solar Dashboard application.
PATTERN: Based on examples/daily_deye_scraper.py for API credentials.
CRITICAL: Uses environment variables for security.
"""

import os
from datetime import timedelta

class Config:
    """Base configuration class with common settings."""
    
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Database settings
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True
    
    # Deye Cloud API credentials - CRITICAL: SHA256 encryption required
    # PATTERN: Following examples/daily_deye_scraper.py authentication
    DEYE_APP_ID = os.environ.get('DEYE_APP_ID') or '202503089003006'
    DEYE_APP_SECRET = os.environ.get('DEYE_APP_SECRET') or 'ed106ac532cee8af0fabe6a6e7a27ecd'
    DEYE_EMAIL = os.environ.get('DEYE_EMAIL') or '<EMAIL>'
    DEYE_PASSWORD = os.environ.get('DEYE_PASSWORD') or 'Dr1jfh0ut@'
    DEYE_BASE_URL = os.environ.get('DEYE_BASE_URL') or 'https://eu1-developer.deyecloud.com'
    
    # APScheduler settings
    SCHEDULER_API_ENABLED = True
    SCHEDULER_TIMEZONE = 'UTC'
    
    # Data collection intervals (in minutes)
    LIVE_DATA_INTERVAL = 5  # Every 5 minutes for live data
    DAILY_DATA_HOUR = 23    # Collect daily data at 23:59
    DAILY_DATA_MINUTE = 59
    
    # API rate limiting settings
    MAX_RETRIES = 3
    RETRY_DELAY = 60  # seconds
    
    @staticmethod
    def init_app(app):
        """Initialize application with this configuration."""
        pass


class DevelopmentConfig(Config):
    """Development configuration with debug enabled and SQLite database."""
    
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(os.path.dirname(__file__)), 'solar_dashboard_dev.db')
    
    # More verbose logging in development
    SQLALCHEMY_ECHO = True
    
    @staticmethod
    def init_app(app):
        Config.init_app(app)
        
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            if not os.path.exists('logs'):
                os.mkdir('logs')
            
            file_handler = RotatingFileHandler('logs/solar_dashboard.log',
                                               maxBytes=10240, backupCount=10)
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('Solar Dashboard startup')


class ProductionConfig(Config):
    """Production configuration with PostgreSQL database."""
    
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://user:password@localhost/solar_dashboard'
    
    # Production security settings
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    @staticmethod
    def init_app(app):
        Config.init_app(app)
        
        # Log to syslog in production
        import logging
        from logging.handlers import SysLogHandler
        
        syslog_handler = SysLogHandler()
        syslog_handler.setLevel(logging.WARNING)
        app.logger.addHandler(syslog_handler)


class TestingConfig(Config):
    """Testing configuration with in-memory database."""
    
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False
    
    # Disable scheduler in testing
    SCHEDULER_API_ENABLED = False
    
    # Faster intervals for testing
    LIVE_DATA_INTERVAL = 1  # 1 minute for testing
    
    @staticmethod
    def init_app(app):
        Config.init_app(app)


# Configuration dictionary for easy access
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
