"""
Main routes for dashboard web interface.
PATTERN: Flask blueprint for web page routes.
CRITICAL: Now syncs real stations from Deye Cloud API.
"""

import logging
from flask import Blueprint, render_template, current_app, flash, redirect, url_for
from app.models.station import Station
from app.services.deye_service import DeyeStationService

logger = logging.getLogger(__name__)
main_bp = Blueprint('main', __name__)


@main_bp.route('/')
def dashboard():
    """
    Main dashboard page.
    CRITICAL: Renders the animated dashboard interface with real API data.
    """
    try:
        # Try to sync stations from Deye Cloud API if none exist
        stations = Station.query.filter_by(is_active=True).all()
        
        if not stations:
            logger.info("No stations found, attempting to sync from Deye Cloud API")
            try:
                deye_service = DeyeStationService()
                synced_stations = deye_service.sync_stations_from_api()
                stations = synced_stations
                if synced_stations:
                    flash(f'Successfully synced {len(synced_stations)} stations from Deye Cloud API', 'success')
                else:
                    flash('No stations found in your Deye Cloud account. Please check your API configuration.', 'warning')
            except Exception as e:
                logger.error(f"Failed to sync stations from API: {e}")
                flash('Failed to connect to Deye Cloud API. Please check your API credentials.', 'error')
        
        return render_template('dashboard.html', 
                             stations=stations,
                             page_title='Solar Dashboard')
    
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        flash('Dashboard error occurred. Please try again.', 'error')
        return render_template('dashboard.html', 
                             stations=[],
                             page_title='Solar Dashboard')


@main_bp.route('/sync-stations')
def sync_stations():
    """
    Manual station sync from Deye Cloud API.
    PATTERN: Allows manual refresh of station list.
    """
    try:
        deye_service = DeyeStationService()
        synced_stations = deye_service.sync_stations_from_api()
        
        if synced_stations:
            flash(f'Successfully synced {len(synced_stations)} stations from Deye Cloud API', 'success')
        else:
            flash('No stations found in your Deye Cloud account.', 'warning')
            
    except Exception as e:
        logger.error(f"Station sync failed: {e}")
        flash(f'Failed to sync stations: {e}', 'error')
    
    return redirect(url_for('main.dashboard'))


@main_bp.route('/station/<int:station_id>')
def station_detail(station_id):
    """
    Detailed view for a specific station.
    """
    station = Station.query.get_or_404(station_id)
    
    return render_template('station_detail.html',
                         station=station,
                         page_title=f'Station: {station.name}')


@main_bp.route('/about')
def about():
    """
    About page with system information.
    """
    return render_template('about.html',
                         page_title='About Solar Dashboard')
