"""
Main routes for dashboard web interface.
PATTERN: Flask blueprint for web page routes.
"""

from flask import Blueprint, render_template, current_app
from app.models.station import Station

main_bp = Blueprint('main', __name__)


@main_bp.route('/')
def dashboard():
    """
    Main dashboard page.
    CRITICAL: Renders the animated dashboard interface.
    """
    # Get active stations for dashboard initialization
    stations = Station.query.filter_by(is_active=True).all()
    
    return render_template('dashboard.html', 
                         stations=stations,
                         page_title='Solar Dashboard')


@main_bp.route('/station/<int:station_id>')
def station_detail(station_id):
    """
    Detailed view for a specific station.
    """
    station = Station.query.get_or_404(station_id)
    
    return render_template('station_detail.html',
                         station=station,
                         page_title=f'Station: {station.name}')


@main_bp.route('/about')
def about():
    """
    About page with system information.
    """
    return render_template('about.html',
                         page_title='About Solar Dashboard')
