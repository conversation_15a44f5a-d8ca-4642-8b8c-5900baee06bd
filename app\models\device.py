"""
Device model for storing solar system device information.
PATTERN: SQLAlchemy model representing inverters, batteries, etc.
"""

from datetime import datetime
from app import db


class Device(db.Model):
    """
    Represents a device in a solar installation (inverter, battery, etc.).
    CRITICAL: device_sn must match Deye Cloud API device serial number.
    """
    __tablename__ = 'devices'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Deye Cloud API fields
    device_sn = db.Column(db.String(50), unique=True, nullable=False, index=True)
    device_type = db.Column(db.String(50), nullable=False)  # INVERTER, BATTERY, METER, etc.
    
    # Relationships
    station_id = db.Column(db.Integer, db.ForeignKey('stations.id'), nullable=False)
    
    # Device details
    model = db.Column(db.String(100))
    firmware_version = db.Column(db.String(50))
    manufacturer = db.Column(db.String(100), default='Deye')
    
    # Status tracking
    is_active = db.Column(db.Bo<PERSON>, default=True, nullable=False)
    last_seen = db.Column(db.DateTime)
    device_state = db.Column(db.Integer)  # 1=Online, 2=Alert, 3=Offline
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    measurements = db.relationship('DeviceMeasurement', backref='device', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Device {self.device_sn} ({self.device_type})>'
    
    def to_dict(self):
        """Convert device to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'device_sn': self.device_sn,
            'device_type': self.device_type,
            'station_id': self.station_id,
            'model': self.model,
            'firmware_version': self.firmware_version,
            'manufacturer': self.manufacturer,
            'is_active': self.is_active,
            'last_seen': self.last_seen.isoformat() if self.last_seen else None,
            'device_state': self.device_state,
            'device_state_text': self.get_device_state_text(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def get_device_state_text(self):
        """Convert device state integer to human-readable text."""
        state_map = {
            1: 'Online',
            2: 'Alert', 
            3: 'Offline'
        }
        return state_map.get(self.device_state, 'Unknown')
    
    def get_latest_measurements(self, limit=10):
        """Get the most recent measurements for this device."""
        return self.measurements.order_by(
            DeviceMeasurement.timestamp.desc()
        ).limit(limit)
    
    def get_measure_point_value(self, measure_point):
        """Get the latest value for a specific measure point."""
        from app.models.measurement import DeviceMeasurement
        measurement = self.measurements.filter_by(
            measure_point=measure_point
        ).order_by(DeviceMeasurement.timestamp.desc()).first()
        
        return measurement.value if measurement else None
    
    @staticmethod
    def create_from_api_data(api_data, station_id):
        """
        Create device from Deye Cloud API device data.
        PATTERN: Factory method for API data conversion.
        """
        device = Device(
            device_sn=api_data.get('deviceSn'),
            device_type=api_data.get('deviceType', 'UNKNOWN'),
            station_id=station_id,
            device_state=api_data.get('deviceState', 3),  # Default to offline
            last_seen=datetime.utcnow()
        )
        return device
