"""
Measurement models for storing solar system data.
PATTERN: SQLAlchemy models for station and device measurements.
CRITICAL: Supports multiple granularities (live, daily, monthly).
"""

from datetime import datetime
from app import db


class StationMeasurement(db.Model):
    """
    Stores aggregated measurements at the station level.
    CRITICAL: Granularity determines data collection frequency and storage.
    """
    __tablename__ = 'station_measurements'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Foreign key relationship
    station_id = db.Column(db.Integer, db.ForeignKey('stations.id'), nullable=False)
    
    # Temporal data
    timestamp = db.Column(db.DateTime, nullable=False, index=True)
    granularity = db.Column(db.String(20), nullable=False, index=True)  # 'live', 'daily', 'monthly'
    
    # Power values (kW) - PATTERN: Following Deye API response structure
    generation_power = db.Column(db.Float)  # Current PV generation
    consumption_power = db.Column(db.Float)  # Household consumption (load)
    grid_power = db.Column(db.Float)  # Grid import (+) / export (-)
    battery_power = db.Column(db.Float)  # Battery charge (+) / discharge (-)
    battery_soc = db.Column(db.Float)  # Battery State of Charge (%)
    
    # Energy values (kWh) - PATTERN: Following Deye API response structure
    generation_value = db.Column(db.Float)  # Total generation
    consumption_value = db.Column(db.Float)  # Total consumption
    grid_value = db.Column(db.Float)  # Total grid import
    purchase_value = db.Column(db.Float)  # Total purchased from grid
    charge_value = db.Column(db.Float)  # Total battery charge
    discharge_value = db.Column(db.Float)  # Total battery discharge
    
    # Additional metrics
    efficiency = db.Column(db.Float)  # System efficiency %
    co2_saved = db.Column(db.Float)  # CO2 savings in kg
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Indexes for efficient queries
    __table_args__ = (
        db.Index('idx_station_timestamp_granularity', 'station_id', 'timestamp', 'granularity'),
        db.Index('idx_timestamp_granularity', 'timestamp', 'granularity'),
    )
    
    def __repr__(self):
        return f'<StationMeasurement {self.station_id} {self.timestamp} {self.granularity}>'
    
    def to_dict(self):
        """Convert measurement to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'station_id': self.station_id,
            'timestamp': self.timestamp.isoformat(),
            'granularity': self.granularity,
            # Power values
            'generation_power': self.generation_power,
            'consumption_power': self.consumption_power,
            'grid_power': self.grid_power,
            'battery_power': self.battery_power,
            'battery_soc': self.battery_soc,
            # Energy values
            'generation_value': self.generation_value,
            'consumption_value': self.consumption_value,
            'grid_value': self.grid_value,
            'purchase_value': self.purchase_value,
            'charge_value': self.charge_value,
            'discharge_value': self.discharge_value,
            # Additional metrics
            'efficiency': self.efficiency,
            'co2_saved': self.co2_saved,
            'created_at': self.created_at.isoformat()
        }
    
    @staticmethod
    def create_from_api_data(api_data, station_id, granularity='live'):
        """
        Create measurement from Deye Cloud API response.
        PATTERN: Factory method for API data conversion.
        CRITICAL: Maps API response fields to database columns.
        """
        # Handle different API response structures
        if granularity == 'live':
            # Real-time data structure
            measurement = StationMeasurement(
                station_id=station_id,
                timestamp=datetime.utcnow(),
                granularity=granularity,
                generation_power=api_data.get('generationPower'),
                consumption_power=api_data.get('consumptionPower'), 
                grid_power=api_data.get('gridPower'),
                battery_power=api_data.get('batteryPower'),
                battery_soc=api_data.get('batterySOC')
            )
        else:
            # Historical data structure (daily/monthly)
            measurement = StationMeasurement(
                station_id=station_id,
                timestamp=api_data.get('timestamp', datetime.utcnow()),
                granularity=granularity,
                generation_value=api_data.get('generationValue'),
                consumption_value=api_data.get('consumptionValue'),
                grid_value=api_data.get('gridValue'),
                purchase_value=api_data.get('purchaseValue'),
                charge_value=api_data.get('chargeValue'),
                discharge_value=api_data.get('dischargeValue')
            )
        
        return measurement


class DeviceMeasurement(db.Model):
    """
    Stores individual device measurements with specific measure points.
    CRITICAL: Flexible schema to handle various device types and measure points.
    """
    __tablename__ = 'device_measurements'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Foreign key relationship
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id'), nullable=False)
    
    # Temporal data
    timestamp = db.Column(db.DateTime, nullable=False, index=True)
    
    # Measurement data - PATTERN: Following Deye API device data structure
    measure_point = db.Column(db.String(100), nullable=False)  # e.g., "SOC", "MI Voltage L2"
    value = db.Column(db.String(50), nullable=False)  # String to handle various data types
    unit = db.Column(db.String(20))  # e.g., "V", "A", "kW", "%"
    
    # Data quality indicators
    quality = db.Column(db.String(20), default='good')  # good, warning, error
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Indexes for efficient queries
    __table_args__ = (
        db.Index('idx_device_timestamp_measure', 'device_id', 'timestamp', 'measure_point'),
        db.Index('idx_measure_point_timestamp', 'measure_point', 'timestamp'),
    )
    
    def __repr__(self):
        return f'<DeviceMeasurement {self.device_id} {self.measure_point}={self.value}{self.unit}>'
    
    def to_dict(self):
        """Convert measurement to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'device_id': self.device_id,
            'timestamp': self.timestamp.isoformat(),
            'measure_point': self.measure_point,
            'value': self.value,
            'unit': self.unit,
            'quality': self.quality,
            'created_at': self.created_at.isoformat()
        }
    
    def get_numeric_value(self):
        """
        Convert string value to numeric for calculations.
        CRITICAL: Handle various data formats from API.
        """
        try:
            return float(self.value)
        except (ValueError, TypeError):
            return None
    
    @staticmethod
    def create_from_api_data(api_data, device_id, timestamp=None):
        """
        Create device measurement from Deye Cloud API response.
        PATTERN: Factory method for API data conversion.
        """
        if timestamp is None:
            timestamp = datetime.utcnow()
            
        measurement = DeviceMeasurement(
            device_id=device_id,
            timestamp=timestamp,
            measure_point=api_data.get('key'),
            value=str(api_data.get('value', '')),
            unit=api_data.get('unit')
        )
        return measurement
