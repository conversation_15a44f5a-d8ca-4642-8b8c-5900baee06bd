"""
Solar data model for storing real-time and historical data from Deye Cloud API.
PATTERN: Optimized for time-series data with proper indexing.
CRITICAL: Maps directly to Deye Cloud API response format.
"""

from datetime import datetime
from sqlalchemy import Index
from app import db


class SolarData(db.Model):
    """
    Solar data measurements from Deye Cloud API.
    PATTERN: Time-series data with foreign key to Station.
    CRITICAL: Stores both real-time and historical data efficiently.
    """
    
    __tablename__ = 'solar_data'
    
    # Primary key and relationships
    id = db.Column(db.Integer, primary_key=True)
    station_id = db.Column(db.Integer, db.ForeignKey('stations.id'), nullable=False)
    
    # Timestamp and data type
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    data_type = db.Column(db.String(20), nullable=False, default='live')  # 'live', 'daily', 'monthly'
    
    # Power measurements (kW)
    pv_generation = db.Column(db.Float, default=0.0)      # Current PV generation
    battery_power = db.Column(db.Float, default=0.0)      # Battery charge/discharge power
    grid_power = db.Column(db.Float, default=0.0)         # Grid import/export power
    load_power = db.Column(db.Float, default=0.0)         # Load consumption power
    
    # Energy measurements (kWh) - typically for daily/monthly data
    total_generation = db.Column(db.Float, default=0.0)   # Total energy generated
    total_consumption = db.Column(db.Float, default=0.0)  # Total energy consumed
    grid_import = db.Column(db.Float, default=0.0)        # Total grid import
    grid_export = db.Column(db.Float, default=0.0)        # Total grid export (usually 0)
    
    # Battery and system status
    battery_soc = db.Column(db.Float, default=0.0)        # Battery State of Charge (%)
    efficiency = db.Column(db.Float, default=0.0)         # System efficiency (%)
    
    # Additional metadata
    additional_data = db.Column(db.JSON, default=dict)    # Extra fields from API
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    station = db.relationship('Station', backref=db.backref('solar_data', lazy='dynamic'))
    
    # Indexes for efficient querying
    __table_args__ = (
        Index('idx_solar_data_station_timestamp', 'station_id', 'timestamp'),
        Index('idx_solar_data_station_type_timestamp', 'station_id', 'data_type', 'timestamp'),
        Index('idx_solar_data_timestamp', 'timestamp'),
    )
    
    def __repr__(self):
        return f'<SolarData {self.station_id}@{self.timestamp} PV:{self.pv_generation}kW>'
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'station_id': self.station_id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'data_type': self.data_type,
            'pv_generation': self.pv_generation,
            'battery_power': self.battery_power,
            'grid_power': self.grid_power,
            'load_power': self.load_power,
            'total_generation': self.total_generation,
            'total_consumption': self.total_consumption,
            'grid_import': self.grid_import,
            'grid_export': self.grid_export,
            'battery_soc': self.battery_soc,
            'efficiency': self.efficiency,
            'additional_data': self.additional_data,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def get_latest_for_station(cls, station_id, data_type='live'):
        """Get the most recent data for a station."""
        return cls.query.filter_by(
            station_id=station_id,
            data_type=data_type
        ).order_by(cls.timestamp.desc()).first()
    
    @classmethod
    def get_range_for_station(cls, station_id, start_date, end_date, data_type='live'):
        """Get data for a station within a date range."""
        return cls.query.filter(
            cls.station_id == station_id,
            cls.data_type == data_type,
            cls.timestamp >= start_date,
            cls.timestamp <= end_date
        ).order_by(cls.timestamp.asc()).all()
    
    @classmethod 
    def get_daily_summary(cls, station_id, date):
        """Get daily summary data for a station."""
        start_date = datetime.combine(date, datetime.min.time())
        end_date = datetime.combine(date, datetime.max.time())
        
        return cls.query.filter(
            cls.station_id == station_id,
            cls.data_type == 'daily',
            cls.timestamp >= start_date,
            cls.timestamp <= end_date
        ).first()
