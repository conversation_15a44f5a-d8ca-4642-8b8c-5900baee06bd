"""
Station model for storing solar installation information.
PATTERN: SQLAlchemy model with relationships to devices and measurements.
"""

from datetime import datetime
from app import db


class Station(db.Model):
    """
    Represents a solar installation station.
    CRITICAL: station_id must match Deye Cloud API station identifier.
    """
    __tablename__ = 'stations'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Deye Cloud API fields
    station_id = db.Column(db.String(50), unique=True, nullable=False, index=True)
    name = db.Column(db.String(200), nullable=False)
    location = db.Column(db.String(200))
    capacity = db.Column(db.Float)  # kW capacity
    
    # Installation details
    installation_date = db.Column(db.Date)
    timezone = db.Column(db.String(50), default='UTC')
    
    # Status tracking
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    last_data_update = db.Column(db.DateTime)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships - CRITICAL: backref creates reverse relationships
    devices = db.relationship('Device', backref='station', lazy='dynamic', cascade='all, delete-orphan')
    measurements = db.relationship('StationMeasurement', backref='station', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Station {self.name} ({self.station_id})>'
    
    def to_dict(self):
        """Convert station to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'station_id': self.station_id,
            'name': self.name,
            'location': self.location,
            'capacity': self.capacity,
            'installation_date': self.installation_date.isoformat() if self.installation_date else None,
            'timezone': self.timezone,
            'is_active': self.is_active,
            'last_data_update': self.last_data_update.isoformat() if self.last_data_update else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def get_latest_measurement(self):
        """Get the most recent live measurement for this station."""
        return self.measurements.filter_by(granularity='live').order_by(
            StationMeasurement.timestamp.desc()
        ).first()
    
    def get_measurements_by_date_range(self, start_date, end_date, granularity='live'):
        """Get measurements within a date range."""
        from app.models.measurement import StationMeasurement
        return self.measurements.filter(
            StationMeasurement.timestamp >= start_date,
            StationMeasurement.timestamp <= end_date,
            StationMeasurement.granularity == granularity
        ).order_by(StationMeasurement.timestamp.asc())
    
    @staticmethod
    def create_from_api_data(api_data):
        """
        Create station from Deye Cloud API station list response.
        PATTERN: Factory method for API data conversion.
        """
        station = Station(
            station_id=str(api_data.get('id')),
            name=api_data.get('name', 'Unknown Station'),
            location=api_data.get('address'),
            capacity=api_data.get('capacity')
        )
        return station
