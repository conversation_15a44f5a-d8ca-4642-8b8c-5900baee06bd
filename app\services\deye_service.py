"""
Deye Cloud API service for fetching and syncing real station data.
PATTERN: Based on examples/daily_deye_scraper.py and research documentation.
CRITICAL: Intelligent API usage with proper rate limiting and caching.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Any
from flask import current_app

from app import db
from app.models import Station
from app.models.solar_data import SolarData
from app.api.deye_client import DeyeAPIClient, DeyeAPIError

logger = logging.getLogger(__name__)


class DeyeStationService:
    """
    Service for managing Deye Cloud API station operations.
    PATTERN: Intelligent API usage - fetch only when needed.
    CRITICAL: Respects API rate limits and implements proper error handling.
    """
    
    def __init__(self):
        """Initialize the service with API client."""
        self.api_client = DeyeAPIClient()
    
    def sync_stations_from_api(self) -> List[Station]:
        """
        Fetch stations from Deye Cloud API and sync to local database.
        PATTERN: Intelligent sync - only updates when necessary.
        
        Returns:
            List of synced stations
        """
        try:
            logger.info("Starting station sync from Deye Cloud API")
            
            # Get token first
            token = self.api_client.get_token()
            if not token:
                raise DeyeAPIError("Failed to obtain API token")
            
            # Fetch station list from API
            api_stations = self._fetch_station_list(token)
            if not api_stations:
                logger.warning("No stations found in Deye Cloud API")
                return []
            
            synced_stations = []
            
            for api_station in api_stations:
                try:
                    station = self._sync_single_station(api_station)
                    if station:
                        synced_stations.append(station)
                except Exception as e:
                    logger.error(f"Failed to sync station {api_station.get('id', 'unknown')}: {e}")
                    continue
            
            db.session.commit()
            logger.info(f"Successfully synced {len(synced_stations)} stations")
            return synced_stations
            
        except Exception as e:
            logger.error(f"Station sync failed: {e}")
            db.session.rollback()
            raise DeyeAPIError(f"Station sync failed: {e}")
    
    def _fetch_station_list(self, token: str) -> List[Dict[str, Any]]:
        """
        Fetch station list from Deye Cloud API.
        PATTERN: Based on examples/daily_deye_scraper.py implementation.
        """
        url = f"{self.api_client.base_url}/v1.0/station/list"
        headers = {
            "Content-Type": "application/json",
            "authorization": f"Bearer {token}",
        }
        data = {"page": 1, "size": 200}  # Get up to 200 stations
        
        try:
            self.api_client._wait_for_rate_limit()
            response = self.api_client.session.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            json_response = response.json()
            
            if json_response.get("success"):
                station_list = json_response.get("stationList", [])
                logger.info(f"Fetched {len(station_list)} stations from API")
                return station_list
            else:
                error_msg = json_response.get('msg', 'Unknown error')
                raise DeyeAPIError(f"Station list retrieval failed: {error_msg}")
                
        except Exception as e:
            logger.error(f"Failed to fetch station list: {e}")
            raise DeyeAPIError(f"Failed to fetch station list: {e}")
    
    def _sync_single_station(self, api_station: Dict[str, Any]) -> Optional[Station]:
        """
        Sync a single station from API data to local database.
        PATTERN: Update existing or create new station.
        """
        try:
            station_id = str(api_station.get('id'))
            station_name = api_station.get('stationName', f'Station {station_id}')
            location = api_station.get('location', 'Unknown Location')
            capacity = float(api_station.get('capacity', 0.0))
            
            # Check if station already exists
            station = Station.query.filter_by(deye_station_id=station_id).first()
            
            if station:
                # Update existing station
                station.name = station_name
                station.location = location
                station.capacity = capacity
                station.last_sync = datetime.utcnow()
                logger.debug(f"Updated existing station: {station_name}")
            else:
                # Create new station
                station = Station(
                    name=station_name,
                    location=location,
                    capacity=capacity,
                    deye_station_id=station_id,
                    api_key_encrypted='',  # Will be populated from config when needed
                    configuration={
                        'timezone': api_station.get('timezone', 'UTC'),
                        'installedTime': api_station.get('installedTime'),
                        'stationType': api_station.get('stationType'),
                        'status': api_station.get('status')
                    },
                    created_at=datetime.utcnow(),
                    last_sync=datetime.utcnow()
                )
                db.session.add(station)
                logger.info(f"Created new station: {station_name}")
            
            return station
            
        except Exception as e:
            logger.error(f"Failed to sync station {api_station.get('id', 'unknown')}: {e}")
            return None
    
    def fetch_station_latest_data(self, station: Station) -> Optional[Dict[str, Any]]:
        """
        Fetch latest real-time data for a specific station.
        PATTERN: Used for live dashboard updates every 5 minutes.
        """
        try:
            token = self.api_client.get_token()
            if not token:
                raise DeyeAPIError("Failed to obtain API token")
            
            url = f"{self.api_client.base_url}/v1.0/station/latest"
            headers = {
                "Content-Type": "application/json",
                "authorization": f"Bearer {token}",
            }
            data = {"stationId": int(station.deye_station_id)}
            
            self.api_client._wait_for_rate_limit()
            response = self.api_client.session.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            json_response = response.json()
            
            if json_response.get("success"):
                # Log the full response for debugging
                logger.info(f"Raw API response for {station.name}: {json_response}")
                logger.info(f"Fetched latest data for station {station.name}: generation={json_response.get('generationPower', 0)}W, consumption={json_response.get('consumptionPower', 0)}W, battery={json_response.get('batterySOC', 0)}%")
                return json_response
            else:
                error_msg = json_response.get('msg', 'Unknown error')
                logger.warning(f"Latest data retrieval failed for {station.name}: {error_msg}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to fetch latest data for station {station.name}: {e}")
            return None
    
    def fetch_station_history_data(self, station: Station, start_date: datetime, end_date: datetime, granularity: int = 1) -> List[Dict[str, Any]]:
        """
        Fetch historical data for a specific station and date range.
        PATTERN: Granularity 1=5min, 2=daily, 3=monthly
        """
        try:
            token = self.api_client.get_token()
            if not token:
                raise DeyeAPIError("Failed to obtain API token")
            
            url = f"{self.api_client.base_url}/v1.0/station/history"
            headers = {
                "Content-Type": "application/json",
                "authorization": f"Bearer {token}",
            }
            
            # Format dates for API
            start_str = start_date.strftime('%Y-%m-%d')
            end_str = end_date.strftime('%Y-%m-%d')
            
            data = {
                "stationId": int(station.deye_station_id),
                "granularity": granularity,
                "startAt": start_str,
                "endAt": end_str
            }
            
            self.api_client._wait_for_rate_limit()
            response = self.api_client.session.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            json_response = response.json()
            
            if json_response.get("success"):
                history_data = json_response.get("stationDataItems", [])
                logger.debug(f"Fetched {len(history_data)} history records for station {station.name}")
                return history_data
            else:
                error_msg = json_response.get('msg', 'Unknown error')
                logger.warning(f"History data retrieval failed for {station.name}: {error_msg}")
                return []
                
        except Exception as e:
            logger.error(f"Failed to fetch history data for station {station.name}: {e}")
            return []
    
    def store_latest_data_to_db(self, station: Station, api_data: Dict[str, Any]) -> Optional[SolarData]:
        """
        Store latest API data to database.
        PATTERN: Convert API format to our database model.
        """
        try:
            # Extract data from API response format
            timestamp = datetime.utcnow()
            
            try:
                # Map API fields to our database fields - handle None values
                logger.debug(f"Creating SolarData object with fields:")
                logger.debug(f"  generationPower: {api_data.get('generationPower')} (type: {type(api_data.get('generationPower'))})")
                logger.debug(f"  batterySOC: {api_data.get('batterySOC')} (type: {type(api_data.get('batterySOC'))})")
                logger.debug(f"  batteryPower: {api_data.get('batteryPower')} (type: {type(api_data.get('batteryPower'))})")
                logger.debug(f"  gridPower: {api_data.get('gridPower')} (type: {type(api_data.get('gridPower'))})")
                logger.debug(f"  consumptionPower: {api_data.get('consumptionPower')} (type: {type(api_data.get('consumptionPower'))})")
                logger.debug(f"  purchasePower: {api_data.get('purchasePower')} (type: {type(api_data.get('purchasePower'))})")
                
                # Convert fields with proper None handling and error reporting
                try:
                    pv_generation = float(api_data.get('generationPower') or 0.0)
                except (TypeError, ValueError) as e:
                    logger.error(f"Error converting generationPower: {api_data.get('generationPower')} - {e}")
                    pv_generation = 0.0
                
                try:
                    battery_soc = float(api_data.get('batterySOC') or 0.0)
                except (TypeError, ValueError) as e:
                    logger.error(f"Error converting batterySOC: {api_data.get('batterySOC')} - {e}")
                    battery_soc = 0.0
                
                try:
                    battery_power = float(api_data.get('batteryPower') or 0.0)
                except (TypeError, ValueError) as e:
                    logger.error(f"Error converting batteryPower: {api_data.get('batteryPower')} - {e}")
                    battery_power = 0.0
                
                try:
                    grid_power = float(api_data.get('gridPower') or 0.0)
                except (TypeError, ValueError) as e:
                    logger.error(f"Error converting gridPower: {api_data.get('gridPower')} - {e}")
                    grid_power = 0.0
                
                try:
                    load_power = float(api_data.get('consumptionPower') or 0.0)
                except (TypeError, ValueError) as e:
                    logger.error(f"Error converting consumptionPower: {api_data.get('consumptionPower')} - {e}")
                    load_power = 0.0
                
                try:
                    total_generation = float(api_data.get('generationPower') or 0.0)
                except (TypeError, ValueError) as e:
                    logger.error(f"Error converting generationPower (total): {api_data.get('generationPower')} - {e}")
                    total_generation = 0.0
                
                try:
                    total_consumption = float(api_data.get('consumptionPower') or 0.0)
                except (TypeError, ValueError) as e:
                    logger.error(f"Error converting consumptionPower (total): {api_data.get('consumptionPower')} - {e}")
                    total_consumption = 0.0
                
                try:
                    grid_import = float(api_data.get('purchasePower') or 0.0)
                except (TypeError, ValueError) as e:
                    logger.error(f"Error converting purchasePower: {api_data.get('purchasePower')} - {e}")
                    grid_import = 0.0

                solar_data = SolarData(
                    station_id=station.id,
                    timestamp=timestamp,
                    pv_generation=pv_generation,  # Current PV generation in kW
                    battery_soc=battery_soc,  # Battery State of Charge %
                    battery_power=battery_power,  # Battery power in kW
                    grid_power=grid_power,  # Grid power in kW
                    load_power=load_power,  # Load consumption in kW
                    total_generation=total_generation,  # Use current as total for now
                    total_consumption=total_consumption,  # Use current as total for now
                    grid_import=grid_import,  # Total daily grid import
                    efficiency=0.0,  # Default efficiency since not provided by API
                    additional_data={
                        'wirePower': api_data.get('wirePower'),
                        'chargePower': api_data.get('chargePower'),
                        'dischargePower': api_data.get('dischargePower'),
                        'irradiateIntensity': api_data.get('irradiateIntensity'),
                        'lastUpdateTime': api_data.get('lastUpdateTime'),
                        'requestId': api_data.get('requestId')
                    }
                )
                logger.debug(f"Successfully created SolarData object")
                
                db.session.add(solar_data)
                db.session.commit()
                
                logger.debug(f"Stored latest data for station {station.name}")
                return solar_data
                
            except Exception as e:
                logger.error(f"Error creating SolarData object: {e}")
                logger.error(f"Full API response: {api_data}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                raise
            
        except Exception as e:
            logger.error(f"Failed to store latest data for station {station.name}: {e}")
            db.session.rollback()
            return None
