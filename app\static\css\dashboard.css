/* 
 * Dashboard-specific styles for Solar Dashboard
 * PATTERN: Animated live cards and solar-themed styling
 */

/* Live cards styling */
.live-card {
    border-radius: var(--card-border-radius);
    border: none;
    box-shadow: var(--shadow-normal);
    transition: all var(--transition-speed) ease;
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.live-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: shimmer 2s infinite;
    z-index: 1;
}

.live-card.solar {
    border-left: 4px solid var(--solar-yellow);
    background: linear-gradient(135deg, #fff8dc 0%, #fffacd 100%);
}

.live-card.solar::before {
    background: linear-gradient(90deg, transparent, var(--solar-yellow), transparent);
}

.live-card.battery {
    border-left: 4px solid var(--battery-green);
    background: linear-gradient(135deg, #f0fff4 0%, #f5fffa 100%);
}

.live-card.battery::before {
    background: linear-gradient(90deg, transparent, var(--battery-green), transparent);
}

.live-card.grid {
    border-left: 4px solid var(--grid-blue);
    background: linear-gradient(135deg, #f0f8ff 0%, #f5f9ff 100%);
}

.live-card.grid::before {
    background: linear-gradient(90deg, transparent, var(--grid-blue), transparent);
}

.live-card.consumption {
    border-left: 4px solid var(--consumption-purple);
    background: linear-gradient(135deg, #f8f0ff 0%, #faf5ff 100%);
}

.live-card.consumption::before {
    background: linear-gradient(90deg, transparent, var(--consumption-purple), transparent);
}

/* Live card animations */
.live-card.updating {
    animation: pulse 1s ease-in-out;
}

.live-card.positive .value {
    animation: glow 2s ease-in-out infinite alternate;
}

/* Live card icons */
.live-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    transition: transform var(--transition-speed) ease;
}

.live-card:hover .live-icon {
    transform: scale(1.1);
}

.live-card.solar .live-icon {
    color: var(--solar-yellow);
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.live-card.battery .live-icon {
    color: var(--battery-green);
}

.live-card.grid .live-icon {
    color: var(--grid-blue);
}

.live-card.consumption .live-icon {
    color: var(--consumption-purple);
}

/* Value displays */
.value {
    font-size: 2rem;
    font-weight: bold;
    margin: 0.5rem 0;
    position: relative;
}

.unit {
    font-size: 1rem;
    font-weight: normal;
    color: #6c757d;
}

.label {
    font-size: 0.9rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.5rem;
}

/* Status indicators */
.status-indicator {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-indicator.offline {
    background-color: var(--danger-color);
}

.status-indicator.warning {
    background-color: var(--warning-color);
}

/* Chart containers */
.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-light);
    padding: 1rem;
}

.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

.chart-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--danger-color);
    z-index: 10;
}

/* Chart controls */
.chart-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.chart-controls .btn-group {
    border-radius: calc(var(--card-border-radius) / 2);
}

.chart-controls input[type="date"] {
    border-radius: calc(var(--card-border-radius) / 2);
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    width: 150px;
}

.chart-controls .btn {
    border-radius: 0;
}

.chart-controls .btn:first-child {
    border-top-left-radius: calc(var(--card-border-radius) / 2);
    border-bottom-left-radius: calc(var(--card-border-radius) / 2);
}

.chart-controls .btn:last-child {
    border-top-right-radius: calc(var(--card-border-radius) / 2);
    border-bottom-right-radius: calc(var(--card-border-radius) / 2);
}

/* Chart legends */
.chart-legend {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 2px;
}

/* Station selection */
.station-selector {
    margin-bottom: 2rem;
}

.station-card {
    border: 2px solid transparent;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    border-radius: var(--card-border-radius);
}

.station-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.station-card.active {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.station-card .card-body {
    text-align: center;
    padding: 1.5rem;
}

.station-card .station-name {
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.station-card .station-info {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Data freshness indicators */
.data-freshness {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.data-freshness.fresh {
    color: var(--success-color);
}

.data-freshness.stale {
    color: var(--warning-color);
}

.data-freshness.old {
    color: var(--danger-color);
}

/* Performance indicators */
.performance-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.performance-card {
    background: white;
    border-radius: var(--card-border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-speed) ease;
}

.performance-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.performance-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.performance-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.performance-change {
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.performance-change.positive {
    color: var(--success-color);
}

.performance-change.negative {
    color: var(--danger-color);
}

/* Error states */
.no-data-message {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.no-data-message i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.error-message {
    text-align: center;
    padding: 2rem;
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
    border-radius: var(--card-border-radius);
    background-color: rgba(220, 53, 69, 0.05);
}

/* Loading states */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
}

.skeleton-text {
    height: 1.2em;
    margin-bottom: 0.5rem;
}

.skeleton-number {
    height: 2em;
    width: 60%;
    margin: 0.5rem auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .live-card {
        margin-bottom: 1rem;
    }
    
    .value {
        font-size: 1.5rem;
    }
    
    .live-icon {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .performance-overview {
        grid-template-columns: 1fr;
    }
    
    .chart-legend {
        gap: 1rem;
    }
    
    .station-card .card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .chart-container {
        height: 250px;
        padding: 0.5rem;
    }
    
    .performance-value {
        font-size: 2rem;
    }
    
    .chart-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .chart-controls .btn-group {
        width: 100%;
    }
    
    .chart-controls input[type="date"] {
        width: 100%;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .live-card {
        border: 2px solid #000;
    }
    
    .status-indicator {
        border: 2px solid #fff;
    }
    
    .chart-container {
        border: 1px solid #000;
    }
}
