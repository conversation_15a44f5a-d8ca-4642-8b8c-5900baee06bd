/* 
 * Main stylesheet for Solar Dashboard
 * PATTERN: Modern CSS with animations and responsive design
 */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --danger-color: #dc3545;
    
    --solar-yellow: #FFD700;
    --battery-green: #28a745;
    --grid-blue: #007bff;
    --consumption-purple: #6f42c1;
    
    --card-border-radius: 0.75rem;
    --transition-speed: 0.3s;
    --shadow-light: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-normal: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Global styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    flex-direction: column;
}

.loading-spinner {
    text-align: center;
}

/* Utility classes */
.fade-in {
    animation: fadeIn var(--transition-speed) ease-in;
}

.fade-out {
    animation: fadeOut var(--transition-speed) ease-out;
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.bounce {
    animation: bounce 1s ease-in-out;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes glow {
    from { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
    to { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Navigation improvements */
.navbar-brand i {
    animation: pulse 2s infinite;
}

#connection-status.bg-success {
    animation: pulse 2s infinite;
}

#connection-status.bg-danger {
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Card enhancements */
.card {
    border: none;
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-light);
    transition: all var(--transition-speed) ease;
}

.card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

/* Footer styling */
.footer {
    margin-top: auto;
    border-top: 1px solid #dee2e6;
}

/* Button improvements */
.btn {
    transition: all var(--transition-speed) ease;
}

.btn:hover {
    transform: translateY(-1px);
}

#manual-refresh-btn.loading {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Form improvements */
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    border-color: var(--primary-color);
}

/* Error page styling */
.error-page {
    padding: 3rem 0;
}

.error-page .display-1 {
    font-size: 8rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 0 0.5rem;
    }
    
    .live-card {
        margin-bottom: 1rem;
    }
    
    .chart-controls {
        flex-direction: column;
        gap: 1rem;
    }
    
    .chart-controls .btn-group {
        width: 100%;
    }
    
    .chart-controls input[type="date"] {
        width: 100% !important;
    }
}

@media (max-width: 576px) {
    .error-page .display-1 {
        font-size: 4rem;
    }
    
    .navbar-nav {
        text-align: center;
    }
    
    .navbar-text {
        text-align: center;
        margin: 1rem 0;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #e9ecef;
        --card-bg: #2d2d2d;
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .card {
        background-color: var(--card-bg);
        border: 1px solid #444;
    }
    
    .navbar-dark {
        background-color: #212529 !important;
    }
    
    .footer {
        background-color: #212529 !important;
        border-top-color: #444;
    }
}

/* Print styles */
@media print {
    .navbar,
    .footer,
    .btn,
    #manual-refresh-btn {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}
