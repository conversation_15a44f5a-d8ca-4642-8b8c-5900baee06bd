/**
 * Animation utilities for Solar Dashboard
 * PATTERN: CSS animations and JavaScript transitions
 */

class AnimationManager {
    constructor() {
        this.observers = new Map();
        this.animationQueue = [];
        this.isAnimating = false;
        
        this.init();
    }
    
    /**
     * Initialize animation system
     */
    init() {
        this.setupIntersectionObserver();
        this.setupAnimationQueue();
        this.bindEvents();
    }
    
    /**
     * Setup intersection observer for scroll animations
     */
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.intersectionObserver = new IntersectionObserver(
                (entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            this.animateElement(entry.target);
                        }
                    });
                },
                {
                    threshold: 0.1,
                    rootMargin: '50px'
                }
            );
        }
    }
    
    /**
     * Setup animation queue processor
     */
    setupAnimationQueue() {
        this.processQueue = () => {
            if (this.animationQueue.length > 0 && !this.isAnimating) {
                this.isAnimating = true;
                const animation = this.animationQueue.shift();
                this.executeAnimation(animation).finally(() => {
                    this.isAnimating = false;
                    if (this.animationQueue.length > 0) {
                        requestAnimationFrame(this.processQueue);
                    }
                });
            }
        };
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // Observe elements with animation classes
        document.addEventListener('DOMContentLoaded', () => {
            this.observeAnimationElements();
        });
        
        // Re-observe when new content is added
        const mutationObserver = new MutationObserver(() => {
            this.observeAnimationElements();
        });
        
        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * Observe elements that should animate on scroll
     */
    observeAnimationElements() {
        const elements = document.querySelectorAll('[data-animate], .animate-on-scroll');
        elements.forEach(element => {
            if (this.intersectionObserver && !this.observers.has(element)) {
                this.intersectionObserver.observe(element);
                this.observers.set(element, true);
            }
        });
    }
    
    /**
     * Animate element when it comes into view
     */
    animateElement(element) {
        const animationType = element.dataset.animate || 'fade-in';
        const delay = parseInt(element.dataset.animateDelay || '0');
        const duration = parseInt(element.dataset.animateDuration || '300');
        
        setTimeout(() => {
            element.classList.add(animationType, 'animated');
        }, delay);
    }
    
    /**
     * Queue an animation
     */
    queueAnimation(animation) {
        this.animationQueue.push(animation);
        requestAnimationFrame(this.processQueue);
    }
    
    /**
     * Execute a single animation
     */
    async executeAnimation(animation) {
        return new Promise((resolve) => {
            const { element, type, options = {} } = animation;
            
            switch (type) {
                case 'fade-in':
                    this.fadeIn(element, options).then(resolve);
                    break;
                case 'fade-out':
                    this.fadeOut(element, options).then(resolve);
                    break;
                case 'slide-in':
                    this.slideIn(element, options).then(resolve);
                    break;
                case 'slide-out':
                    this.slideOut(element, options).then(resolve);
                    break;
                case 'bounce':
                    this.bounce(element, options).then(resolve);
                    break;
                case 'pulse':
                    this.pulse(element, options).then(resolve);
                    break;
                case 'shake':
                    this.shake(element, options).then(resolve);
                    break;
                case 'glow':
                    this.glow(element, options).then(resolve);
                    break;
                default:
                    resolve();
            }
        });
    }
    
    /**
     * Fade in animation
     */
    async fadeIn(element, options = {}) {
        const duration = options.duration || 300;
        const easing = options.easing || 'ease-in-out';
        
        element.style.opacity = '0';
        element.style.transition = `opacity ${duration}ms ${easing}`;
        
        // Force reflow
        element.offsetHeight;
        
        element.style.opacity = '1';
        
        return new Promise(resolve => {
            setTimeout(resolve, duration);
        });
    }
    
    /**
     * Fade out animation
     */
    async fadeOut(element, options = {}) {
        const duration = options.duration || 300;
        const easing = options.easing || 'ease-in-out';
        
        element.style.opacity = '1';
        element.style.transition = `opacity ${duration}ms ${easing}`;
        
        // Force reflow
        element.offsetHeight;
        
        element.style.opacity = '0';
        
        return new Promise(resolve => {
            setTimeout(() => {
                if (options.hide) {
                    element.style.display = 'none';
                }
                resolve();
            }, duration);
        });
    }
    
    /**
     * Slide in animation
     */
    async slideIn(element, options = {}) {
        const duration = options.duration || 300;
        const direction = options.direction || 'up';
        const distance = options.distance || '20px';
        const easing = options.easing || 'ease-out';
        
        let transform = '';
        switch (direction) {
            case 'up':
                transform = `translateY(${distance})`;
                break;
            case 'down':
                transform = `translateY(-${distance})`;
                break;
            case 'left':
                transform = `translateX(${distance})`;
                break;
            case 'right':
                transform = `translateX(-${distance})`;
                break;
        }
        
        element.style.opacity = '0';
        element.style.transform = transform;
        element.style.transition = `opacity ${duration}ms ${easing}, transform ${duration}ms ${easing}`;
        
        // Force reflow
        element.offsetHeight;
        
        element.style.opacity = '1';
        element.style.transform = 'translate(0, 0)';
        
        return new Promise(resolve => {
            setTimeout(resolve, duration);
        });
    }
    
    /**
     * Slide out animation
     */
    async slideOut(element, options = {}) {
        const duration = options.duration || 300;
        const direction = options.direction || 'up';
        const distance = options.distance || '20px';
        const easing = options.easing || 'ease-in';
        
        let transform = '';
        switch (direction) {
            case 'up':
                transform = `translateY(-${distance})`;
                break;
            case 'down':
                transform = `translateY(${distance})`;
                break;
            case 'left':
                transform = `translateX(-${distance})`;
                break;
            case 'right':
                transform = `translateX(${distance})`;
                break;
        }
        
        element.style.transition = `opacity ${duration}ms ${easing}, transform ${duration}ms ${easing}`;
        element.style.opacity = '0';
        element.style.transform = transform;
        
        return new Promise(resolve => {
            setTimeout(() => {
                if (options.hide) {
                    element.style.display = 'none';
                }
                resolve();
            }, duration);
        });
    }
    
    /**
     * Bounce animation
     */
    async bounce(element, options = {}) {
        const duration = options.duration || 600;
        const intensity = options.intensity || 1;
        
        element.style.animation = `bounce ${duration}ms ease-in-out`;
        element.style.transformOrigin = 'center bottom';
        
        return new Promise(resolve => {
            setTimeout(() => {
                element.style.animation = '';
                resolve();
            }, duration);
        });
    }
    
    /**
     * Pulse animation
     */
    async pulse(element, options = {}) {
        const duration = options.duration || 1000;
        const scale = options.scale || 1.05;
        const iterations = options.iterations || 1;
        
        element.style.animation = `pulse ${duration}ms ease-in-out ${iterations}`;
        
        return new Promise(resolve => {
            setTimeout(() => {
                element.style.animation = '';
                resolve();
            }, duration * iterations);
        });
    }
    
    /**
     * Shake animation
     */
    async shake(element, options = {}) {
        const duration = options.duration || 500;
        const intensity = options.intensity || 1;
        
        const keyframes = [
            { transform: 'translateX(0)' },
            { transform: `translateX(-${10 * intensity}px)` },
            { transform: `translateX(${10 * intensity}px)` },
            { transform: `translateX(-${10 * intensity}px)` },
            { transform: `translateX(${10 * intensity}px)` },
            { transform: 'translateX(0)' }
        ];
        
        const animation = element.animate(keyframes, {
            duration: duration,
            easing: 'ease-in-out'
        });
        
        return animation.finished;
    }
    
    /**
     * Glow animation
     */
    async glow(element, options = {}) {
        const duration = options.duration || 2000;
        const color = options.color || '#FFD700';
        const intensity = options.intensity || 0.8;
        
        const keyframes = [
            { boxShadow: '0 0 5px rgba(255, 215, 0, 0.2)' },
            { boxShadow: `0 0 20px rgba(255, 215, 0, ${intensity})` },
            { boxShadow: '0 0 5px rgba(255, 215, 0, 0.2)' }
        ];
        
        const animation = element.animate(keyframes, {
            duration: duration,
            easing: 'ease-in-out',
            iterations: options.iterations || 1
        });
        
        return animation.finished;
    }
    
    /**
     * Number counting animation
     */
    async countUp(element, endValue, options = {}) {
        const duration = options.duration || 1000;
        const startValue = options.startValue || 0;
        const decimals = options.decimals || 1;
        const prefix = options.prefix || '';
        const suffix = options.suffix || '';
        
        const startTime = performance.now();
        const range = endValue - startValue;
        
        return new Promise(resolve => {
            const updateNumber = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Easing function (ease-out cubic)
                const easedProgress = 1 - Math.pow(1 - progress, 3);
                
                const currentValue = startValue + (range * easedProgress);
                element.textContent = `${prefix}${currentValue.toFixed(decimals)}${suffix}`;
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                } else {
                    element.textContent = `${prefix}${endValue.toFixed(decimals)}${suffix}`;
                    resolve();
                }
            };
            
            requestAnimationFrame(updateNumber);
        });
    }
    
    /**
     * Stagger animation for multiple elements
     */
    async staggerAnimation(elements, animationType, options = {}) {
        const delay = options.staggerDelay || 100;
        const animationOptions = { ...options };
        delete animationOptions.staggerDelay;
        
        const promises = Array.from(elements).map((element, index) => {
            return new Promise(resolve => {
                setTimeout(() => {
                    this.queueAnimation({
                        element,
                        type: animationType,
                        options: animationOptions
                    });
                    resolve();
                }, index * delay);
            });
        });
        
        return Promise.all(promises);
    }
    
    /**
     * Utility methods for live card animations
     */
    animateLiveCardUpdate(card) {
        card.classList.add('updating');
        
        // Remove class after animation
        setTimeout(() => {
            card.classList.remove('updating');
        }, 1000);
    }
    
    animateValueChange(element, newValue, oldValue, options = {}) {
        const isIncrease = newValue > oldValue;
        const changeClass = isIncrease ? 'value-increase' : 'value-decrease';
        
        element.classList.add(changeClass);
        
        // Count up animation
        this.countUp(element, newValue, {
            startValue: oldValue,
            ...options
        });
        
        // Remove class after animation
        setTimeout(() => {
            element.classList.remove(changeClass);
        }, options.duration || 1000);
    }
    
    animateChartUpdate(chartContainer) {
        const canvas = chartContainer.querySelector('canvas');
        if (canvas) {
            canvas.style.opacity = '0.7';
            canvas.style.transform = 'scale(0.98)';
            canvas.style.transition = 'opacity 300ms ease, transform 300ms ease';
            
            setTimeout(() => {
                canvas.style.opacity = '1';
                canvas.style.transform = 'scale(1)';
            }, 100);
            
            setTimeout(() => {
                canvas.style.transition = '';
            }, 400);
        }
    }
    
    /**
     * Loading animations
     */
    showLoadingAnimation(element, message = 'Loading...') {
        element.innerHTML = `
            <div class="loading-animation">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">${message}</span>
                </div>
                <div class="loading-text mt-2">${message}</div>
            </div>
        `;
        
        this.fadeIn(element, { duration: 200 });
    }
    
    hideLoadingAnimation(element) {
        const loadingEl = element.querySelector('.loading-animation');
        if (loadingEl) {
            this.fadeOut(loadingEl, { duration: 200, hide: true });
        }
    }
    
    /**
     * Error animations
     */
    showErrorAnimation(element, message) {
        element.classList.add('error-shake');
        this.shake(element, { intensity: 0.5 });
        
        setTimeout(() => {
            element.classList.remove('error-shake');
        }, 500);
    }
    
    /**
     * Success animations
     */
    showSuccessAnimation(element) {
        element.classList.add('success-glow');
        this.glow(element, { color: '#28a745', intensity: 0.6 });
        
        setTimeout(() => {
            element.classList.remove('success-glow');
        }, 2000);
    }
    
    /**
     * Cleanup
     */
    destroy() {
        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect();
        }
        
        this.observers.clear();
        this.animationQueue = [];
    }
}

// Initialize animation manager
let animationManager;

document.addEventListener('DOMContentLoaded', () => {
    animationManager = new AnimationManager();
    
    // Extend dashboard with animation methods
    if (window.solarDashboard) {
        window.solarDashboard.animationManager = animationManager;
        
        // Override animateCardUpdate to use animation manager
        window.solarDashboard.animateCardUpdate = function(card, newValue, unit) {
            const valueElement = card.querySelector('.value');
            const cardElement = card.closest('.live-card');
            
            if (!valueElement) return;
            
            // Get current value
            const currentValue = parseFloat(valueElement.textContent) || 0;
            
            // Animate the card
            animationManager.animateLiveCardUpdate(cardElement);
            
            // Animate the value change
            animationManager.animateValueChange(valueElement, newValue, currentValue, {
                duration: 500,
                decimals: 1,
                suffix: ` ${unit}`
            });
            
            // Add positive glow effect for positive values
            if (newValue > 0) {
                cardElement.classList.add('positive');
                animationManager.glow(cardElement, { 
                    color: this.getCardColor(cardElement),
                    intensity: 0.3,
                    duration: 1000
                });
            } else {
                cardElement.classList.remove('positive');
            }
        };
        
        // Helper to get card theme color
        window.solarDashboard.getCardColor = function(card) {
            if (card.classList.contains('solar')) return '#FFD700';
            if (card.classList.contains('battery')) return '#28a745';
            if (card.classList.contains('grid')) return '#007bff';
            if (card.classList.contains('consumption')) return '#6f42c1';
            return '#007bff';
        };
        
        // Override cleanup to include animation cleanup
        const originalCleanup = window.solarDashboard.cleanup;
        window.solarDashboard.cleanup = function() {
            if (this.animationManager) {
                this.animationManager.destroy();
            }
            originalCleanup.call(this);
        };
    }
});

// Export for use in other modules
window.AnimationManager = AnimationManager;
