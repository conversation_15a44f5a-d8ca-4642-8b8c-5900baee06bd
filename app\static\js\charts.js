/**
 * Chart management for Solar Dashboard
 * PATTERN: Chart.js integration with real-time updates
 */

class ChartManager {
    constructor() {
        this.charts = {};
        this.chartConfigs = {};
        this.defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index',
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        displayFormats: {
                            hour: 'HH:mm',
                            day: 'MMM DD',
                            month: 'MMM YYYY'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += context.formattedValue;
                            if (context.dataset.unit) {
                                label += ' ' + context.dataset.unit;
                            }
                            return label;
                        }
                    }
                }
            },
            animation: {
                duration: 500,
                easing: 'easeInOutQuart'
            }
        };
        
        this.colorScheme = {
            solar: '#FFD700',
            battery: '#28a745',
            grid: '#007bff',
            consumption: '#6f42c1',
            voltage: '#fd7e14',
            current: '#20c997',
            temperature: '#dc3545'
        };
    }
    
    /**
     * Initialize all charts
     */
    async init() {
        this.setupChartContainers();
        this.setupChartControls();
        await this.createPowerChart();
        await this.createEnergyChart();
        await this.createEfficiencyChart();
    }
    
    /**
     * Setup chart containers
     */
    setupChartContainers() {
        const containers = [
            { id: 'power-chart-container', canvasId: 'powerChart' },
            { id: 'energy-chart-container', canvasId: 'energyChart' },
            { id: 'efficiency-chart-container', canvasId: 'efficiencyChart' }
        ];
        
        containers.forEach(container => {
            const element = document.getElementById(container.id);
            if (element && !element.querySelector('canvas')) {
                element.innerHTML = `
                    <canvas id="${container.canvasId}"></canvas>
                    <div class="chart-loading" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="mt-2">Loading chart data...</div>
                    </div>
                    <div class="chart-error" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div class="mt-2">Failed to load chart data</div>
                    </div>
                    <div class="no-data-message" style="display: none;">
                        <i class="fas fa-chart-line"></i>
                        <div class="mt-2">No data available for selected period</div>
                    </div>
                `;
            }
        });
    }
    
    /**
     * Setup chart controls
     */
    setupChartControls() {
        // Period selector
        const periodSelector = document.querySelector('.chart-period-selector');
        if (periodSelector) {
            periodSelector.addEventListener('change', (e) => {
                this.updateAllCharts(e.target.value);
            });
        }
        
        // Date selector
        const dateSelector = document.querySelector('.chart-date-selector');
        if (dateSelector) {
            dateSelector.addEventListener('change', (e) => {
                this.updateAllCharts('custom', e.target.value);
            });
        }
        
        // Export buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-chart-btn')) {
                const chartId = e.target.dataset.chartId;
                this.exportChart(chartId);
            }
        });
    }
    
    /**
     * Create power flow chart
     */
    async createPowerChart() {
        const canvas = document.getElementById('powerChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        const config = {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Solar Power',
                        data: [],
                        borderColor: this.colorScheme.solar,
                        backgroundColor: this.colorScheme.solar + '20',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        unit: 'kW'
                    },
                    {
                        label: 'Battery Power',
                        data: [],
                        borderColor: this.colorScheme.battery,
                        backgroundColor: this.colorScheme.battery + '20',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        unit: 'kW'
                    },
                    {
                        label: 'Grid Power',
                        data: [],
                        borderColor: this.colorScheme.grid,
                        backgroundColor: this.colorScheme.grid + '20',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        unit: 'kW'
                    },
                    {
                        label: 'Consumption',
                        data: [],
                        borderColor: this.colorScheme.consumption,
                        backgroundColor: this.colorScheme.consumption + '20',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        unit: 'kW'
                    }
                ]
            },
            options: {
                ...this.defaultOptions,
                scales: {
                    ...this.defaultOptions.scales,
                    y: {
                        ...this.defaultOptions.scales.y,
                        title: {
                            display: true,
                            text: 'Power (kW)'
                        }
                    }
                },
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: 'Power Flow Over Time'
                    }
                }
            }
        };
        
        this.charts.power = new Chart(ctx, config);
        this.chartConfigs.power = config;
    }
    
    /**
     * Create energy chart
     */
    async createEnergyChart() {
        const canvas = document.getElementById('energyChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        const config = {
            type: 'bar',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Solar Generation',
                        data: [],
                        backgroundColor: this.colorScheme.solar,
                        borderColor: this.colorScheme.solar,
                        borderWidth: 1,
                        unit: 'kWh'
                    },
                    {
                        label: 'Battery Charged',
                        data: [],
                        backgroundColor: this.colorScheme.battery,
                        borderColor: this.colorScheme.battery,
                        borderWidth: 1,
                        unit: 'kWh'
                    },
                    {
                        label: 'Grid Export',
                        data: [],
                        backgroundColor: this.colorScheme.grid,
                        borderColor: this.colorScheme.grid,
                        borderWidth: 1,
                        unit: 'kWh'
                    },
                    {
                        label: 'Consumption',
                        data: [],
                        backgroundColor: this.colorScheme.consumption,
                        borderColor: this.colorScheme.consumption,
                        borderWidth: 1,
                        unit: 'kWh'
                    }
                ]
            },
            options: {
                ...this.defaultOptions,
                scales: {
                    ...this.defaultOptions.scales,
                    y: {
                        ...this.defaultOptions.scales.y,
                        title: {
                            display: true,
                            text: 'Energy (kWh)'
                        },
                        stacked: false
                    },
                    x: {
                        ...this.defaultOptions.scales.x,
                        stacked: false
                    }
                },
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: 'Energy Production and Consumption'
                    }
                }
            }
        };
        
        this.charts.energy = new Chart(ctx, config);
        this.chartConfigs.energy = config;
    }
    
    /**
     * Create efficiency chart
     */
    async createEfficiencyChart() {
        const canvas = document.getElementById('efficiencyChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        const config = {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'System Efficiency',
                        data: [],
                        borderColor: this.colorScheme.solar,
                        backgroundColor: this.colorScheme.solar + '20',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        unit: '%',
                        yAxisID: 'y'
                    },
                    {
                        label: 'Temperature',
                        data: [],
                        borderColor: this.colorScheme.temperature,
                        backgroundColor: this.colorScheme.temperature + '20',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.4,
                        unit: '°C',
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                ...this.defaultOptions,
                scales: {
                    ...this.defaultOptions.scales,
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Efficiency (%)'
                        },
                        beginAtZero: true,
                        max: 100
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Temperature (°C)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: 'System Efficiency and Temperature'
                    }
                }
            }
        };
        
        this.charts.efficiency = new Chart(ctx, config);
        this.chartConfigs.efficiency = config;
    }
    
    /**
     * Update all charts with new data
     */
    async updateAllCharts(period = 'today', customDate = null) {
        if (!window.solarDashboard?.currentStationId) return;
        
        try {
            this.showAllChartsLoading();
            
            // Build API endpoint
            let endpoint = `/api/stations/${window.solarDashboard.currentStationId}/history?period=${period}`;
            if (customDate) {
                endpoint += `&date=${customDate}`;
            }
            
            const response = await window.solarDashboard.apiCall(endpoint);
            const data = response.data;
            
            if (data && data.length > 0) {
                await this.updatePowerChart(data);
                await this.updateEnergyChart(data);
                await this.updateEfficiencyChart(data);
                this.hideAllChartsLoading();
            } else {
                this.showNoDataMessage();
            }
            
        } catch (error) {
            console.error('Failed to update charts:', error);
            this.showChartsError('Failed to load chart data');
        }
    }
    
    /**
     * Update power chart
     */
    async updatePowerChart(data) {
        const chart = this.charts.power;
        if (!chart) return;
        
        const labels = data.map(item => new Date(item.timestamp));
        const solarData = data.map(item => item.solar_power || 0);
        const batteryData = data.map(item => item.battery_power || 0);
        const gridData = data.map(item => item.grid_power || 0);
        const consumptionData = data.map(item => item.consumption || 0);
        
        chart.data.labels = labels;
        chart.data.datasets[0].data = solarData;
        chart.data.datasets[1].data = batteryData;
        chart.data.datasets[2].data = gridData;
        chart.data.datasets[3].data = consumptionData;
        
        chart.update('none');
        
        // Animate chart appearance
        setTimeout(() => {
            chart.update('active');
        }, 100);
    }
    
    /**
     * Update energy chart
     */
    async updateEnergyChart(data) {
        const chart = this.charts.energy;
        if (!chart) return;
        
        // Aggregate data by hour/day depending on period
        const aggregatedData = this.aggregateEnergyData(data);
        
        const labels = aggregatedData.map(item => item.label);
        const solarData = aggregatedData.map(item => item.solar_energy || 0);
        const batteryData = aggregatedData.map(item => item.battery_energy || 0);
        const gridData = aggregatedData.map(item => item.grid_energy || 0);
        const consumptionData = aggregatedData.map(item => item.consumption_energy || 0);
        
        chart.data.labels = labels;
        chart.data.datasets[0].data = solarData;
        chart.data.datasets[1].data = batteryData;
        chart.data.datasets[2].data = gridData;
        chart.data.datasets[3].data = consumptionData;
        
        chart.update('none');
        
        // Animate chart appearance
        setTimeout(() => {
            chart.update('active');
        }, 100);
    }
    
    /**
     * Update efficiency chart
     */
    async updateEfficiencyChart(data) {
        const chart = this.charts.efficiency;
        if (!chart) return;
        
        const labels = data.map(item => new Date(item.timestamp));
        const efficiencyData = data.map(item => {
            const solar = item.solar_power || 0;
            const consumption = item.consumption || 0;
            return consumption > 0 ? Math.min((solar / consumption) * 100, 100) : 0;
        });
        const temperatureData = data.map(item => item.temperature || 25);
        
        chart.data.labels = labels;
        chart.data.datasets[0].data = efficiencyData;
        chart.data.datasets[1].data = temperatureData;
        
        chart.update('none');
        
        // Animate chart appearance
        setTimeout(() => {
            chart.update('active');
        }, 100);
    }
    
    /**
     * Aggregate energy data for bar chart
     */
    aggregateEnergyData(data) {
        // Group by hour for today, by day for other periods
        const groups = {};
        
        data.forEach(item => {
            const date = new Date(item.timestamp);
            const key = date.toISOString().split('T')[0]; // Group by date for now
            
            if (!groups[key]) {
                groups[key] = {
                    label: date,
                    solar_energy: 0,
                    battery_energy: 0,
                    grid_energy: 0,
                    consumption_energy: 0,
                    count: 0
                };
            }
            
            groups[key].solar_energy += item.solar_power || 0;
            groups[key].battery_energy += Math.abs(item.battery_power || 0);
            groups[key].grid_energy += Math.max(0, item.grid_power || 0);
            groups[key].consumption_energy += item.consumption || 0;
            groups[key].count++;
        });
        
        // Convert to array and calculate averages
        return Object.values(groups).map(group => ({
            ...group,
            solar_energy: group.solar_energy / group.count,
            battery_energy: group.battery_energy / group.count,
            grid_energy: group.grid_energy / group.count,
            consumption_energy: group.consumption_energy / group.count
        }));
    }
    
    /**
     * Export chart as image
     */
    exportChart(chartId) {
        const chart = this.charts[chartId];
        if (!chart) return;
        
        const url = chart.toBase64Image();
        const link = document.createElement('a');
        link.download = `${chartId}-chart-${new Date().toISOString().split('T')[0]}.png`;
        link.href = url;
        link.click();
    }
    
    /**
     * Resize all charts
     */
    resizeCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.resize === 'function') {
                chart.resize();
            }
        });
    }
    
    /**
     * UI state management
     */
    showAllChartsLoading() {
        document.querySelectorAll('.chart-loading').forEach(el => {
            el.style.display = 'flex';
        });
        document.querySelectorAll('.chart-error, .no-data-message').forEach(el => {
            el.style.display = 'none';
        });
    }
    
    hideAllChartsLoading() {
        document.querySelectorAll('.chart-loading').forEach(el => {
            el.style.display = 'none';
        });
    }
    
    showChartsError(message) {
        document.querySelectorAll('.chart-loading').forEach(el => {
            el.style.display = 'none';
        });
        document.querySelectorAll('.chart-error').forEach(el => {
            el.textContent = message;
            el.style.display = 'flex';
        });
    }
    
    showNoDataMessage() {
        document.querySelectorAll('.chart-loading').forEach(el => {
            el.style.display = 'none';
        });
        document.querySelectorAll('.no-data-message').forEach(el => {
            el.style.display = 'flex';
        });
    }
    
    /**
     * Cleanup
     */
    destroy() {
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts = {};
    }
}

// Extend the main dashboard class with chart functionality
if (window.solarDashboard) {
    window.solarDashboard.updateCharts = async function(data) {
        if (!this.chartManager) {
            this.chartManager = new ChartManager();
            await this.chartManager.init();
        }
        
        await this.chartManager.updateAllCharts();
    };
    
    window.solarDashboard.updateChartPeriod = function(period) {
        if (this.chartManager) {
            this.chartManager.updateAllCharts(period);
        }
    };
    
    window.solarDashboard.updateChartDate = function(date) {
        if (this.chartManager) {
            this.chartManager.updateAllCharts('custom', date);
        }
    };
    
    // Override cleanup to include chart cleanup
    const originalCleanup = window.solarDashboard.cleanup;
    window.solarDashboard.cleanup = function() {
        if (this.chartManager) {
            this.chartManager.destroy();
        }
        originalCleanup.call(this);
    };
}

// Handle window resize
window.addEventListener('resize', () => {
    if (window.solarDashboard?.chartManager) {
        window.solarDashboard.chartManager.resizeCharts();
    }
});
