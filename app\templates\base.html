<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Solar Dashboard{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.dashboard') }}">
                <i class="fas fa-solar-panel me-2"></i>
                Solar Dashboard
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.dashboard' %}active{% endif %}" 
                           href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.about' %}active{% endif %}" 
                           href="{{ url_for('main.about') }}">
                            <i class="fas fa-info-circle me-1"></i>
                            About
                        </a>
                    </li>
                </ul>
                
                <!-- Status indicator -->
                <div class="navbar-text">
                    <span id="connection-status" class="badge bg-success">
                        <i class="fas fa-wifi"></i> Connected
                    </span>
                    <span id="last-update" class="badge bg-secondary ms-2">
                        <i class="fas fa-clock"></i> <span id="last-update-time">--:--</span>
                    </span>
                </div>
                
                <!-- Manual refresh button -->
                <button id="manual-refresh-btn" class="btn btn-outline-light btn-sm ms-2" title="Refresh Data">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main content -->
    <main class="container-fluid py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer bg-light mt-5 py-3">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        &copy; 2025 Solar Dashboard. Built with Flask & Chart.js.
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        Data from Deye Cloud API
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay d-none">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-3">Loading data...</div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/animations.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
