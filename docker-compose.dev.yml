version: '3.8'

# Development override for docker-compose
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  # Development Flask Application
  web:
    build:
      context: .
      dockerfile: Dockerfile.dev
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - DATABASE_URL=**************************************************/solar_dashboard
      - DEYE_API_URL=https://eu1-developer.deyecloud.com/v1.0
      - SECRET_KEY=dev_secret_key
      - SCHEDULER_ENABLED=true
      - LOG_LEVEL=DEBUG
    volumes:
      - .:/app
      - /app/__pycache__
      - /app/.pytest_cache
    ports:
      - "5000:5000"
    command: flask run --host=0.0.0.0 --port=5000 --reload

  # Remove nginx in development
  nginx:
    profiles:
      - production

  # Database with exposed port for development
  db:
    ports:
      - "5432:5432"

  # Redis with exposed port for development
  redis:
    ports:
      - "6379:6379"
