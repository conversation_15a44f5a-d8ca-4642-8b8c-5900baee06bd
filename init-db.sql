-- Initialize Solar Dashboard Database
-- This script creates the initial database schema and any required data

-- Create database if it doesn't exist (handled by docker-compose)
-- CREATE DATABASE IF NOT EXISTS solar_dashboard;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create indexes for better performance (Flask-Migrate will handle the main tables)
-- These will be created after Flask migrations run

-- Insert default configuration data if needed
-- This can be used for application settings

-- Create a function to update timestamps
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Note: The actual table creation will be handled by Flask-Migrate
-- This file is primarily for database initialization and initial data

-- Set timezone
SET timezone = 'UTC';

-- Create application user (if not using the default postgres user)
-- DO $$ 
-- BEGIN
--     IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'solar_app') THEN
--         CREATE ROLE solar_app WITH LOGIN PASSWORD 'solar_app_password';
--         GRANT CONNECT ON DATABASE solar_dashboard TO solar_app;
--         GRANT USAGE ON SCHEMA public TO solar_app;
--         GRANT CREATE ON SCHEMA public TO solar_app;
--     END IF;
-- END
-- $$;
