# Database Migrations Guide

This document explains how to use Flask-Migrate to manage the database schema for the Solar Dashboard application.

## Quick Start

### 1. Initial Setup (Already Done)
```bash
# Initialize migrations (already completed)
flask db init

# Create initial migration (already completed)
flask db migrate -m "Initial migration"
```

### 2. Apply Migrations
```bash
# Apply all pending migrations
flask db upgrade

# Apply to specific revision
flask db upgrade <revision_id>

# Rollback to previous migration
flask db downgrade
```

### 3. Creating New Migrations
```bash
# Generate a new migration after model changes
flask db migrate -m "Add new column to stations table"

# Create empty migration file for manual changes
flask db revision -m "Custom data migration"
```

## Migration Commands Reference

### Basic Commands
- `flask db init` - Initialize migration repository (one-time setup)
- `flask db migrate -m "description"` - Generate new migration
- `flask db upgrade` - Apply pending migrations
- `flask db downgrade` - Rollback last migration
- `flask db current` - Show current revision
- `flask db history` - Show migration history
- `flask db show <revision>` - Show specific migration

### Advanced Commands
- `flask db stamp <revision>` - Mark database as being at specific revision
- `flask db edit <revision>` - Edit a migration file
- `flask db merge <rev1> <rev2>` - Merge two migration branches

## Database Schema

### Current Tables

#### stations
- `id` - Primary key (integer)
- `station_id` - Unique Deye station ID (string)
- `name` - Station display name (string)
- `location` - Station location (optional string)
- `capacity` - Station capacity in kW (float)
- `timezone` - Station timezone (string)
- `created_at` - Record creation timestamp
- `updated_at` - Record update timestamp

#### devices  
- `id` - Primary key (integer)
- `device_sn` - Unique device serial number (string)
- `station_id` - Foreign key to stations.id
- `device_type` - Type of device (string)
- `name` - Device display name (string)
- `model` - Device model (optional string)
- `created_at` - Record creation timestamp
- `updated_at` - Record update timestamp

#### station_measurements
- `id` - Primary key (integer)
- `station_id` - Foreign key to stations.id
- `timestamp` - Measurement timestamp
- `granularity` - Data granularity (live/daily/monthly)
- `solar_power` - Solar generation in kW (float)
- `battery_power` - Battery power in kW (float, + charging, - discharging)
- `battery_level` - Battery level percentage (float)
- `grid_power` - Grid power in kW (float, + import, - export)
- `consumption` - Power consumption in kW (float)
- `voltage` - System voltage (optional float)
- `current` - System current (optional float)
- `temperature` - System temperature (optional float)

#### device_measurements
- `id` - Primary key (integer)
- `device_id` - Foreign key to devices.id
- `timestamp` - Measurement timestamp
- `measure_point` - Specific measurement point identifier (string)
- `value` - Measurement value (float)
- `unit` - Value unit (string)

### Indexes
- `ix_stations_station_id` - Unique index on station_id
- `ix_devices_device_sn` - Unique index on device_sn
- `idx_station_timestamp_granularity` - Composite index for efficient station data queries
- `idx_timestamp_granularity` - Index for time-based queries
- `idx_device_timestamp_measure` - Composite index for device measurements
- `idx_measure_point_timestamp` - Index for measure point queries

## Migration Best Practices

### 1. Always Review Generated Migrations
Before applying migrations, review the generated SQL to ensure it matches your intent:
```bash
# Review pending migrations
flask db show head
```

### 2. Backup Database Before Major Changes
```bash
# For SQLite (development)
cp solar_dashboard.db solar_dashboard.db.backup

# For PostgreSQL (production)
pg_dump solar_dashboard > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 3. Test Migrations in Development First
Always test migrations in development environment before applying to production.

### 4. Data Migrations
For complex data transformations, create custom migration scripts:
```python
# In migration file
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Schema changes
    op.add_column('stations', sa.Column('new_field', sa.String(100)))
    
    # Data migration
    connection = op.get_bind()
    connection.execute(
        "UPDATE stations SET new_field = 'default_value' WHERE new_field IS NULL"
    )

def downgrade():
    op.drop_column('stations', 'new_field')
```

## Environment-Specific Migrations

### Development
```bash
# SQLite database (default)
export DATABASE_URL=sqlite:///solar_dashboard.db
flask db upgrade
```

### Production
```bash
# PostgreSQL database
export DATABASE_URL=postgresql://user:pass@localhost/solar_dashboard
flask db upgrade
```

### Docker
```bash
# Inside container
docker-compose exec web flask db upgrade

# Or run as separate container
docker-compose run --rm web flask db upgrade
```

## Troubleshooting

### Common Issues

1. **Migration conflicts**: When multiple developers create migrations
```bash
# Create merge migration
flask db merge -m "Merge migrations" <rev1> <rev2>
```

2. **Schema drift**: When database doesn't match migrations
```bash
# Check current state
flask db current
flask db show head

# Force to specific revision (dangerous!)
flask db stamp head
```

3. **Failed migration**: When migration fails partway through
```bash
# Check database state
flask db current

# Manually fix issues, then continue
flask db upgrade
```

### Recovery Commands
```bash
# Reset to clean state (DESTRUCTIVE!)
rm migrations/versions/*.py
flask db stamp base
flask db migrate -m "Reset migration"
flask db upgrade
```

## Maintenance Tasks

### Regular Maintenance
- Monitor migration history for unnecessary files
- Clean up old backup files
- Review and optimize database indexes
- Check for unused tables or columns

### Performance Monitoring
```sql
-- Check table sizes (PostgreSQL)
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check index usage (PostgreSQL)
SELECT 
    indexrelname,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
ORDER BY idx_tup_read DESC;
```

## Support

For issues with migrations:
1. Check this README first
2. Review Flask-Migrate documentation
3. Check application logs for specific error messages
4. Test in development environment before production changes
