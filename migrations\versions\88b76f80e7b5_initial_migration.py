"""Initial migration

Revision ID: 88b76f80e7b5
Revises: 
Create Date: 2025-07-17 22:02:47.887627

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '88b76f80e7b5'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('stations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('station_id', sa.String(length=50), nullable=False),
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('location', sa.String(length=200), nullable=True),
    sa.Column('capacity', sa.Float(), nullable=True),
    sa.Column('installation_date', sa.Date(), nullable=True),
    sa.Column('timezone', sa.String(length=50), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('last_data_update', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('stations', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_stations_station_id'), ['station_id'], unique=True)

    op.create_table('devices',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('device_sn', sa.String(length=50), nullable=False),
    sa.Column('device_type', sa.String(length=50), nullable=False),
    sa.Column('station_id', sa.Integer(), nullable=False),
    sa.Column('model', sa.String(length=100), nullable=True),
    sa.Column('firmware_version', sa.String(length=50), nullable=True),
    sa.Column('manufacturer', sa.String(length=100), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('last_seen', sa.DateTime(), nullable=True),
    sa.Column('device_state', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['station_id'], ['stations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('devices', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_devices_device_sn'), ['device_sn'], unique=True)

    op.create_table('station_measurements',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('station_id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('granularity', sa.String(length=20), nullable=False),
    sa.Column('generation_power', sa.Float(), nullable=True),
    sa.Column('consumption_power', sa.Float(), nullable=True),
    sa.Column('grid_power', sa.Float(), nullable=True),
    sa.Column('battery_power', sa.Float(), nullable=True),
    sa.Column('battery_soc', sa.Float(), nullable=True),
    sa.Column('generation_value', sa.Float(), nullable=True),
    sa.Column('consumption_value', sa.Float(), nullable=True),
    sa.Column('grid_value', sa.Float(), nullable=True),
    sa.Column('purchase_value', sa.Float(), nullable=True),
    sa.Column('charge_value', sa.Float(), nullable=True),
    sa.Column('discharge_value', sa.Float(), nullable=True),
    sa.Column('efficiency', sa.Float(), nullable=True),
    sa.Column('co2_saved', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['station_id'], ['stations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('station_measurements', schema=None) as batch_op:
        batch_op.create_index('idx_station_timestamp_granularity', ['station_id', 'timestamp', 'granularity'], unique=False)
        batch_op.create_index('idx_timestamp_granularity', ['timestamp', 'granularity'], unique=False)
        batch_op.create_index(batch_op.f('ix_station_measurements_granularity'), ['granularity'], unique=False)
        batch_op.create_index(batch_op.f('ix_station_measurements_timestamp'), ['timestamp'], unique=False)

    op.create_table('device_measurements',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('device_id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('measure_point', sa.String(length=100), nullable=False),
    sa.Column('value', sa.String(length=50), nullable=False),
    sa.Column('unit', sa.String(length=20), nullable=True),
    sa.Column('quality', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['device_id'], ['devices.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('device_measurements', schema=None) as batch_op:
        batch_op.create_index('idx_device_timestamp_measure', ['device_id', 'timestamp', 'measure_point'], unique=False)
        batch_op.create_index('idx_measure_point_timestamp', ['measure_point', 'timestamp'], unique=False)
        batch_op.create_index(batch_op.f('ix_device_measurements_timestamp'), ['timestamp'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('device_measurements', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_device_measurements_timestamp'))
        batch_op.drop_index('idx_measure_point_timestamp')
        batch_op.drop_index('idx_device_timestamp_measure')

    op.drop_table('device_measurements')
    with op.batch_alter_table('station_measurements', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_station_measurements_timestamp'))
        batch_op.drop_index(batch_op.f('ix_station_measurements_granularity'))
        batch_op.drop_index('idx_timestamp_granularity')
        batch_op.drop_index('idx_station_timestamp_granularity')

    op.drop_table('station_measurements')
    with op.batch_alter_table('devices', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_devices_device_sn'))

    op.drop_table('devices')
    with op.batch_alter_table('stations', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_stations_station_id'))

    op.drop_table('stations')
    # ### end Alembic commands ###
