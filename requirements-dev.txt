# Development dependencies for Solar Dashboard
# These are additional packages for development and testing

# Testing framework
pytest==7.4.3
pytest-flask==1.3.0
pytest-cov==4.1.0
pytest-mock==3.12.0

# Code formatting and linting
black==23.11.0
flake8==6.1.0
isort==5.13.2
mypy==1.7.1

# Development tools
flask-shell-ipython==1.4.0
ipython==8.17.2
ipdb==0.13.13

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Load testing
locust==2.17.0

# Database management
alembic==1.13.1

# Environment management
python-decouple==3.8

# Type stubs
types-requests==*********
types-redis==********
