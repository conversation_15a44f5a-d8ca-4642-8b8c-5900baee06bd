# Solar Dashboard - Python Dependencies
# Production requirements for Flask application

# Core Flask framework
Flask==3.0.0
Flask-SQLAlchemy==3.1.1
Flask-Migrate==4.0.5

# Database drivers
psycopg2-binary==2.9.9
SQLAlchemy==2.0.23

# Scheduling
APScheduler==3.10.4

# HTTP requests
requests==2.31.0
urllib3==2.1.0

# Environment management
python-dotenv==1.0.0

# Production WSGI server
gunicorn==21.2.0

# Caching (optional)
redis==5.0.1
Flask-Caching==2.1.0

# Security and encryption
cryptography==41.0.7
bcrypt==4.1.2

# Date/time handling
pytz==2023.3.post1

# JSON handling
orjson==3.9.10

# Logging
structlog==23.2.0

# Configuration validation
marshmallow==3.20.1

# Development and testing dependencies (optional)
# Uncomment for development environment
# pytest==7.4.3
# pytest-flask==1.3.0
# pytest-cov==4.1.0
# black==23.11.0
# flake8==6.1.0
# mypy==1.7.1

# Monitoring and metrics (optional)
# prometheus-client==0.19.0
# flask-healthcheck==0.1.0
