Title: DeyeCloud Developer Portal - Getting Started
URL Source: https://developer.deyecloud.com/start/

# DeyeCloud OpenAPI Portal - Quick Start Guide

## Introduction

Welcome to the DeyeCloud OpenAPI Portal. The DeyeCloud system's data is accessible securely and efficiently via API. The platform interface offers two primary operations:

1. **Monitoring Operation**: Fetch station-level and device-level data, including historical and real-time data.
2. **Commissioning Operation**: Execute commands to control devices.

## Getting Started Steps

1. Create a DeyeCloud application
2. Obtain access token
3. Invoke endpoints through access token

## Data Center Selection

**Important**: Remember which datacenter you choose when you register your DeyeCloud account.

- **EU Data Center**: If you choose a data center in EU, the baseUrl is `https://eu1-developer.deyecloud.com`
- **US Data Center**: If you choose a data center in US, the baseUrl is `https://us1-developer.deyecloud.com`

### Which data center should I choose?

Depending on the location of your devices:
- If your devices are in Europe, Africa, or the Asia-Pacific region, please choose the Europe data center.
- If your devices are in North or South America, please choose the Americas data center.

## Create a DeyeCloud Application

1. To create a DeyeCloud Application, you must first have an account on DeyeCloud.
2. Fill in the new Application form and create an application.
3. After creating an application, `AppId` & `AppSecret` will be assigned to you. **Please save this information securely.**

## OpenAPI Usage Flow

![Flow Chart](https://developer.deyecloud.com/deyecloud/image/flowUrl.png)

1. Obtain Token
   ↓
2. Obtain Station and Device List under Account
   ├─ Obtain Station Latest Data
   ├─ Obtain Station History Data
   ├─ Obtain Device History Data (granularity ≠ 1)
   ├─ Obtain Device MeasurePoints
   │   └─ Obtain Device History Data (granularity == 1)
   ├─ Obtain Device Latest Data
   └─ Control Device through Commission Operation
       └─ Obtain Commission Result of Command

## Obtain Access Token

### Personal User

**What is Personal User?** You register the account on DeyeCloud as personal user.

**Endpoint**: `{baseUrl}/v1.0/account/token?appId={AppId}`

**Notice**:
- Parameter 'appId' is put on the URL.
- Field 'password' should be in **SHA256 encrypted** and in **lowercase**.
- Multiple token calling will not invalidate the original one.
- Access token will expire after 60 days.
- Access token expires if the password is reset or the role is modified.

#### Sample cURL Request:
```bash
curl --location 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=ffffffff' \
--header 'Content-Type: application/json' \
--data-raw '{
  "appSecret": "ffffffff",
  "email": "<EMAIL>",
  "password": "1d8286c46e8e0d32c0987809114ad8aa41234"
}'
```

#### Sample Python Code - Email Authentication:
```python
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=***************'
    headers = {
        'Content-Type': 'application/json'
    }
    # Body
    data = {
        "appSecret": "af958493485e56e7d58d092ea3473b64",
        "email": "<EMAIL>",  # email of DeyeCloud account
        "password": "1d8286c46e8e0d32cab580009114ad8a2f778642be"  # SHA256 encrypted password
    }
    try:
        # Send POST Request
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        # print response status
        print(response.status_code)
        print(response.json())
    except requests.exceptions.HTTPError as err:
        print(f"HTTP error occurred: {err}")
    except Exception as err:
        print(f"Other error occurred: {err}")
```

#### Sample Python Code - Username Authentication:
```python
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=***************'
    headers = {
        'Content-Type': 'application/json'
    }
    data = {
        "appSecret": "af958493485e56e7d58d092ea3473b64",
        "username": "jbdon",  # username of DeyeCloud account
        "password": "8d969eef6ecad3c29a00000000e6862020c0000000002"  # SHA256 encrypted password
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        print(response.status_code)
        print(response.json())
    except requests.exceptions.HTTPError as err:
        print(f"HTTP error occurred: {err}")
    except Exception as err:
        print(f"Other error occurred: {err}")
```

#### Sample Python Code - Mobile Authentication:
```python
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=***************'
    headers = {
        'Content-Type': 'application/json'
    }
    data = {
        "appSecret": "af958493485e56e7d58d092ea3473b64",
        "mobile": "***********",  # mobile of DeyeCloud account
        "countryCode": 86,  # countryCode of DeyeCloud account
        "password": "1d8286c4600000008a809114ad8aa411805778642be"  # SHA256 encrypted password
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        print(response.status_code)
        print(response.json())
    except requests.exceptions.HTTPError as err:
        print(f"HTTP error occurred: {err}")
    except Exception as err:
        print(f"Other error occurred: {err}")
```

### Business Member

**What is business member?** You register the account on DeyeCloud as business member.

#### Step 1: Get Initial Token
Invoke `{baseUrl}/v1.0/account/token?appId={AppId}` with business member credentials.

#### Step 2: Get Company Information
Invoke `{baseUrl}/v1.0/account/info` with the access token to get 'CompanyId'.

**Sample Request**:
```bash
curl --location --request POST 'https://eu1-developer.deyecloud.com/v1.0/account/info' \
--header 'Content-Type: application/json' \
--header 'Authorization: bearer [ACCESS_TOKEN]' \
--data ''
```

**Sample Response**:
```json
{
    "code": "1000000",
    "msg": "success",
    "success": true,
    "requestId": null,
    "orgInfoList": [
        {
            "companyId": 963,
            "companyName": "Columbia Energy",
            "roleName": "Administrator"
        },
        {
            "companyId": 999,
            "companyName": "Green Energy",
            "roleName": "Developer"
        }
    ]
}
```

#### Step 3: Get Business Member Token
Invoke `{baseUrl}/v1.0/account/token?appId={AppId}` with the `companyId` to get a business member access token.

**Sample Request**:
```bash
curl --location 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=fffffff' \
--header 'Content-Type: application/json' \
--data-raw '{
  "appSecret": "ffffff",
  "email": "<EMAIL>",
  "companyId": 999,
  "password": "1d8286c46e8e0d32cab58abcg32f778642be"
}'
```

**Sample Response**:
```json
{
    "code": "1000000",
    "msg": "success",
    "success": true,
    "requestId": null,
    "accessToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "bearer",
    "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "5183999",
    "scope": "all",
    "uid": 1000
}
```

**Note**: It is recommended that you remember the companyId so that you don't have to fetch it again when you obtain the access token again when it expires.

## Device Control Operations

### Set Time of Use of the Device

**Sample Python Code**:
```python
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/order/sys/tou/update'
    headers = {
        'Authorization': 'bearer [ACCESS_TOKEN]',
        'Content-Type': 'application/json'
    }
    data = {
        "deviceSn": "221299",
        "timeUseSettingItems": [
            {
                "enableGeneration": True,
                "enableGridCharge": True,
                "power": 1000,
                "soc": 20,
                "time": "02:10"
            },
            {
                "enableGeneration": True,
                "enableGridCharge": False,
                "power": 2000,
                "soc": 20,
                "time": "03:15"
            }
        ],
        "timeoutSeconds": 30
    }
    response = requests.post(url, headers=headers, json=data)
    print(response.status_code)
    print(response.json())
```

### Obtain Command Commission Result

**Sample Python Code**:
```python
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/order/6811105'  # 6811105 is the orderId
    headers = {
        'Authorization': 'bearer [ACCESS_TOKEN]',
        'Content-Type': 'application/json;charset=UTF-8'
    }
    response = requests.get(url, headers=headers)
    print(response.status_code)
    print(response.json())
```

## Important Security Notes

1. **Password Encryption**: All passwords must be SHA256 encrypted and in lowercase format.
2. **Token Expiry**: Access tokens expire after 60 days.
3. **Token Invalidation**: Access tokens expire if the password is reset or the role is modified.
4. **Secure Storage**: Store AppId and AppSecret securely.

For detailed API documentation, see: [API Documentation](https://developer.deyecloud.com/api)
