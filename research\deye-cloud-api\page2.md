Title: Deye Cloud API - Complete OpenAPI Documentation
URL Source: https://eu1-developer.deyecloud.com/v2/api-docs

# Deye Cloud OpenAPI Specification (v2.0)

**Host**: eu1-developer.deyecloud.com
**Base Path**: /

## API Tags/Categories

1. **Account Operation** - User authentication and account management
2. **Commission Operation** - Device control commands  
3. **Configuration Operation** - Device configuration retrieval
4. **Device Operation** - Device data and management
5. **Station Operation** - Station-level operations
6. **Strategy Operation** - Strategy management

## Authentication

All API endpoints require Bearer token authentication:
```
Authorization: Bearer {access_token}
```

## Key API Endpoints

### Account Operations

#### `/v1.0/account/token` (POST)
**Obtain Access Token**
- Parameters: `appId` (query), `tokenRequest` (body)
- Supports email, username, or mobile authentication
- Password must be SHA256 encrypted in lowercase
- Optional `companyId` for business member access

**Request Body Schema (tokenRequest)**:
```json
{
  "appSecret": "string",
  "password": "string", // SHA256 encrypted
  "email": "string", // optional
  "username": "string", // optional  
  "mobile": "string", // optional
  "countryCode": "string", // required with mobile
  "companyId": "integer" // optional for business members
}
```

#### `/v1.0/account/info` (POST)
**Query Account Organization Info**
- Returns company relationships and roles for business members

### Device Operations

#### `/v1.0/device/latest` (POST)
**Fetch Latest Device Data**
- Supports batch queries (up to 10 devices)
- Returns real-time device measurements

**Request Body Schema (latestDataRequest)**:
```json
{
  "deviceList": ["device_sn_1", "device_sn_2"]
}
```

#### `/v1.0/device/history` (POST)
**Retrieve Device History Data**
- Multiple granularities supported:
  - `granularity=1`: Daily data with measure points (format: 'yyyy-MM-dd')
  - `granularity=2`: Daily statistics (format: 'yyyy-MM-dd', up to 31 days)
  - `granularity=3`: Monthly statistics (format: 'yyyy-MM', up to 12 months)
  - `granularity=4`: Yearly statistics (format: 'yyyy')

**Request Body Schema (DeyeDeviceHistoryDataRequest)**:
```json
{
  "deviceSn": "string",
  "granularity": 1,
  "startAt": "2024-01-01",
  "endAt": "2024-01-31", // required for granularity 2,3,4
  "measurePoints": ["SOC", "TotalChargeEnergy"] // required for granularity 1
}
```

#### `/v1.0/device/historyRaw` (POST)
**Retrieve Device History by Timestamp**
- Uses millisecond timestamps
- Maximum 5-day range
- Requires measure points

#### `/v1.0/device/measurePoints` (POST)
**Fetch Device Measure Points**
- Returns available data points for a device
- Required for history data queries with granularity=1

#### `/v1.0/device/list` (POST)
**Fetch Device List for Business Members**
- Paginated results (max 200 per page)

#### `/v1.0/device/alertList` (POST)
**Retrieve Device Alert List**
- Uses 10-digit Unix timestamps (seconds)
- Maximum 30-day range

### Station Operations

#### `/v1.0/station/list` (POST)
**Fetch Station List**
- Paginated results (default 20, max 200 per page)

#### `/v1.0/station/real-time-data` (POST)
**Fetch Station Real-time Data**
- Returns current power generation, consumption, battery status

#### `/v1.0/station/history` (POST)
**Retrieve Station History Data**
- Same granularity options as device history
- Aggregated station-level statistics

#### `/v1.0/station/history-by-timestamp` (POST)
**Retrieve Station History by Timestamp**
- Millisecond timestamps
- Maximum range limitations apply

### Commission Operations (Device Control)

#### `/v1.0/order/battery/modeControl` (POST)
**Enable/Disable Battery Charge Modes**
- Modes: `GRID_CHARGE`, `GEN_CHARGE`
- Actions: `on`, `off`

#### `/v1.0/order/battery/parameter/update` (POST)
**Set Battery-Related Parameters**
- Parameters: `MAX_CHARGE_CURRENT`, `MAX_DISCHARGE_CURRENT`, `GRID_CHARGE_AMPERE`, `BATT_LOW`

#### `/v1.0/order/battery/type/update` (POST)
**Set Battery Type**
- LV Hybrid types: `BATT_V`, `BATT_SOC`, `LI`, `NO_BATTERY`
- HV Hybrid types: `BATT_V`, `LI`, `NO_BATTERY`

#### `/v1.0/order/sys/workMode/update` (POST)
**Set System Work Mode**
- Modes: `SELLING_FIRST`, `ZERO_EXPORT_TO_LOAD`, `ZERO_EXPORT_TO_CT`
- Micro storage: `GREEN_POWER_MODE`, `FULL_CHARGE_MODE`, `CUSTOMIZED_MODE`

#### `/v1.0/order/sys/power/update` (POST)
**Set Power Parameters**
- Types: `MAX_SELL_POWER`, `MAX_SOLAR_POWER`, `ZERO_EXPORT_POWER`

#### `/v1.0/order/sys/tou/update` (POST)
**Set Time of Use (TOU) Configuration**
- Configure 6 time intervals
- Each interval has power, SOC, and time settings

**TOU Item Schema (TimeUseSettingItem)**:
```json
{
  "time": "02:10",
  "power": 1000,
  "soc": 20,
  "enableGeneration": true,
  "enableGridCharge": true,
  "voltage": 48 // optional for voltage mode
}
```

#### `/v1.0/order/sys/tou/switch` (POST)
**Enable/Disable TOU Function**
- Supports specific days of week
- Days: `MONDAY`, `TUESDAY`, `WEDNESDAY`, `THURSDAY`, `FRIDAY`, `SATURDAY`, `SUNDAY`

#### `/v1.0/order/smartload/update` (POST)
**Set Smart Load Parameters**
- Configure on/off SOC and voltage thresholds
- Voltage ranges vary by device model:
  - Three phase HV Hybrid: 150-800V
  - Three phase LV Hybrid: 38-60V  
  - Single phase LV Hybrid: 20-60V

#### `/v1.0/order/gridPeakShaving/control` (POST)
**Enable/Disable Grid Peak Shaving**
- Limits grid output power to set value
- Uses PV and battery as supplement

#### `/v1.0/order/{orderId}` (GET)
**Get Commission Command Result**
- Check status of control commands
- Status codes:
  - 0: To be sent
  - 100: Sending
  - 300: During upgrade
  - 400: Terminated
  - 500: Failed
  - 666: Succeeded

### Configuration Operations

#### `/v1.0/config/battery` (POST)
**Obtain Battery-Related Parameters**
- Returns: `battLowCapacity`, `battShutDownCapacity`, `maxChargeCurrent`, `maxDischargeCurrent`

#### `/v1.0/config/system` (POST)
**Obtain System Work Mode Parameters**
- Returns current system configuration

#### `/v1.0/config/tou` (POST)
**Obtain Time of Use Configuration**
- Returns current TOU settings

## Data Models

### Key Response Schemas

#### Standard Response Format
```json
{
  "code": "1000000",
  "msg": "success", 
  "success": true,
  "requestId": "string",
  "data": {} // varies by endpoint
}
```

#### Device Latest Data Response
```json
{
  "deviceDataList": [
    {
      "deviceSn": "12583SS",
      "deviceType": "INVERTER",
      "deviceState": 1, // 1=Online, 2=Alert, 3=Offline
      "collectionTime": 1679980600,
      "dataList": [
        {
          "key": "MI Voltage L2",
          "value": "223.40",
          "unit": "V"
        }
      ]
    }
  ]
}
```

#### Station Data Item
```json
{
  "generationPower": 5.2,
  "consumptionPower": 3.1,
  "gridPower": -2.1,
  "batteryPower": 1.5,
  "batterySOC": 85.5,
  "generationValue": 182.6,
  "consumptionValue": 242.5,
  "timeStamp": "2024-01-01T12:00:00Z"
}
```

### Device Types
- `INVERTER`
- `MICRO_INVERTER`
- `MICRO_STORAGE_IN_ONE`
- `BATTERY`
- `METER`
- `COMBINER_BOX`
- `WEATHER_STATION`
- `MECD`
- `DTU`
- And many more...

### Error Codes
- `1000000`: Success
- `501`: Conflicting Command
- `502`: Product Type Error
- `505`: System Error
- `510`: Parsing Failure
- `530`: Command Failure
- `570`: Firmware package download failure

## Rate Limiting & Best Practices

1. **Token Expiry**: Access tokens expire after 60 days
2. **API Limits**: Batch operations limited (e.g., max 10 devices for latest data)
3. **Date Ranges**: History queries have maximum range limits (30 days for alerts, 5 days for raw data)
4. **Timestamps**: 
   - Alert APIs use 10-digit Unix timestamps (seconds)
   - History raw APIs use millisecond timestamps
5. **Pagination**: Use proper page and size parameters (max 200 per page)

## Regional Endpoints

- **EU**: `https://eu1-developer.deyecloud.com`
- **US**: `https://us1-developer.deyecloud.com`

Choose based on device location:
- EU: Europe, Africa, Asia-Pacific
- US: North and South America

This comprehensive API documentation covers all available endpoints for monitoring and controlling Deye solar inverters and energy storage systems through the cloud platform.
