﻿Title: DeyeCloud Developer Portal

URL Source: https://developer.deyecloud.com/start/

Published Time: Mon, 31 Mar 2025 10:32:18 GMT

Markdown Content:
UPDATED

Updated on Mar 31, 2025

![Image 1](blob:http://localhost/1159988f74ffe3f2e6bdac5a188c883d) 10 minute(s) read

Introduction

Welcome to the DeyeCloud OpenAPI Portal. The DeyeCloud system’s data is accessible securely and efficiently via API. The platform interface offers two primary operations:

Monitoring Operation: Fetch station-level and device-level data, including historical and real-time data.

Commissioning Operation: Execute commands to control devices.

Station-level and device-level data (including history and live data) can be fetched through Monitoring Operations.

Commissioning Operations can be used to commit the command to control the device.

This document will guide you through the registration, setup, usage, and best practices for working with the DeyeCloud OpenAPI.

Create a DeyeCloud application

Obtain access token

Invoke endpoints through access token

Create a DeyeCloud Application

To start, make sure you have completed the following steps:

To create a DeyeCloud Application, you must first have an account on DeyeCloud.

Please remember which datacenter you choose when you register your DeyeCloud account.

If you choose a data center in EU, the baseUrl (for the following endpoint invoking steps) is https://eu1-developer.deyecloud.com.

If you choose a data center in US, the baseUrl (for the following endpoint invoking steps) is https://us1-developer.deyecloud.com.

Which data center should I choose?

Depending on the location of your devices:

If your devices are in Europe, Africa, or the Asia-Pacific region, please choose the Europe data center.

If your devices are in North or South America, please choose the Americas data center.

2. Create a DeyeCloud application

Fill in the new Application form and create an application.

After creating an application, AppId&AppSecret will be assigned to you. Please save this information securely.

Getting Started

The below flow chart describes the flow of OpenAPI usage:

![Image 2](https://developer.deyecloud.com/deyecloud/image/flowUrl.png)

Obtain Access Token

Personal User

What is Personal User? You register the account on DeyeCloud as personal user.

Invoke {baseUrl}/v1.0/account/token?appId={AppId}(you obtained from creating application)

Notice

Parameter 'appId' is put on the URL.

Field 'password' should be in **SHA256 encrypted** and in **lowercase**.

Multiple token calling will not invalidate the original one.

Access token will expire after 60 days.

Access token expires if the password is reset or the role is modified.

```
curl --location 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=ffffffff' --header 'Content-Type: application/json' --data-raw '{
  "appSecret": "ffffffff",
  "email": "<EMAIL>",
  "password": "1d8286c46e8e0d32c0987809114ad8aa41234"
}'
```

Sample Code in Python

Obtain access token through email

```
import requests

if __name__ == '__main__':
  url = 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=***************'
  headers = {
      'Content-Type': 'application/json'
  }
  # Body
  data = {
      "appSecret": "af958493485e56e7d58d092ea3473b64",
      "email": "<EMAIL>",  #email of DeyeCloud account
      "password": "1d8286c46e8e0d32cab580009114ad8a2f778642be" #password of DeyeCloud account
  }
  try:
      # Send POST Request 
      response = requests.post(url, headers=headers, json=data)
      response.raise_for_status()  
      # print response status
      print(response.status_code)
      print(response.json())

  except requests.exceptions.HTTPError as err:
      print(f"HTTP error occurred: {err}")
  except Exception as err:
      print(f"Other error occurred: {err}")
```

Obtain access token through username

```
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=***************'
    headers = {
        'Content-Type': 'application/json'
    }
    data = {
        "appSecret": "af958493485e56e7d58d092ea3473b64",
        "username": "jbdon",  #username of DeyeCloud account
        "password": "8d969eef6ecad3c29a00000000e6862020c0000000002"  #password of DeyeCloud account
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  
        print(response.status_code)
        print(response.json())

    except requests.exceptions.HTTPError as err:
        print(f"HTTP error occurred: {err}")
    except Exception as err:
        print(f"Other error occurred: {err}")
```

Obtain access token through mobile

```
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=***************'
    headers = {
        'Content-Type': 'application/json'
    }
    data = {
        "appSecret": "af958493485e56e7d58d092ea3473b64",
        "mobile": "***********",  #mobile of DeyeCloud account
        "countryCode": 86,  #countryCode of DeyeCloud account
        "password": "1d8286c4600000008a809114ad8aa411805778642be" #password of DeyeCloud account
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  
        print(response.status_code)
        print(response.json())

    except requests.exceptions.HTTPError as err:
        print(f"HTTP error occurred: {err}")
    except Exception as err:
        print(f"Other error occurred: {err}")
```

Business Member

What is business member? You register the account on DeyeCloud as business member.

1. Invoke {baseUrl}/v1.0/account/token?appId={AppId}(you obtained from creating application)

Sample Request:

```
curl --location 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=ffffff' --header 'Content-Type: application/json' --data-raw '{
  "appSecret": "ffffff",
  "email": "<EMAIL>", #email of DeyeCloud account
  "password": "1d8286c46e8e0d32c0987809114ad8aa411805fba861234" #password of DeyeCloud account
}'
```

Sample Response:

```
{
    "code": "1000000",
    "msg": "success",
    "success": true,
    "requestId": null,
    "accessToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsib2F1diOiJ0ZXN0In0.OF_GpXeWB2rkTwAcoPnfqsMRSb5oH4A5DM7Ktlk5z4ihODupDjhcHiDtw8b9Y65bdQqa4K_wjAgISF4aO-t6baVka8Zrv6GA7NxwK8JH6oCMiEUMqwNdfqYXh726EgX8xIKZY3iZ5V9_t6T610qYSHgtQ8kXCh",
    "tokenType": "bearer",
    "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsib2F1dGgyLXJlc291VySWImlkZW50aWZpZXIiOil1bnFpbmdAZGV5ZS5jb20uY24iLCJpZGVudGl0eVR5cGUiOjIsIm1kYyI6InVjIiwiYXBwSWQiOiIyMDI0MDIyODI3NTQwMDIifSwiZRlMDBKcTrFws15KWLcpAHaLDzVpDb1Xc5g",
    "expiresIn": "5183999",
    "scope": "all",
    "uid": 1000
}
```

2. Invoke {baseUrl}/v1.0/account/info with the access token (obtained last step) in the header to get 'CompanyId'.

Sample Request:

```
curl --location --request POST 'https://eu1-developer.deyecloud.com/v1.0/account/info' --header 'Content-Type: application/json' --header 'Authorization: bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsib2F1dGgyLXJlc291cmhhbmd5dW5xaW5nQGRleWUJzY29wZSI6WyJhbGwiXSwiZGV0YWlsIjp7Im9yZ2FuaXphdGlvbklkIjowLCJ0b3BHcm91cElkIjpudWxsLCJncm91cElkIjpudWxsLCJyb2xlSWQiOi0xLCJ1c2VySWQiOjUsInZlcnNpb24iOjEwMDAsImlkZW50aWZpZXIiOiJ6aGFuZ3l1bnFpbmdAZGV5ZS5jb20uY24iLCJpZGVudGl0eVR5cGUiOjIsIm1kYyI6InVjIiwiYXBwSWQiOiIyMDI0MDIyODI3NTQwMDIifSwiZXhwIjoxNzI0MDQxODMwLCJtZGMiOiJ1YyIsImF1dGhvcml0aWVzIjpbImFsbCJdLCJqdGkiOiI5NjMzYTFlOC05YWY4LTRlNjktYTQ2Yi0wYzVjOGU4NGM2MDAiLCJjbGllbnRfaWQiOiJ0ZXN0In0.OF_GpXeWB2rkkCl2YcAKfTwAcoPnfqsMRSb5oH4A5DM7Ktlk5z4ihODupDjhcHiDtw8b9Y65bdQqa4K_wjAgISF4aO-t6baVka8Zrv6GA7NxwK8JH6oCMiEUMqwNdfqYXh726EgX8xIKZY3iZ5V9_t6T610qYSHgtQ8kXChQNO1j5o4UtnY-3q8GV1csqkg93VPOnZAPenZJorJGaWL8DJ8tZR5V-dJaVlpch_WazTKIhnu2QN2a69KWEO_QOxMcj8XNYZCoEbqdjX_yJcWqFN66VogEVkih3hR6Nbv6bo9srBGl7X5BxaDJU9_sKL5yWtYSkHpjyBTO9me__7rP3Q' --data '
```

Sample Response:

```
{
    "code": "1000000",
    "msg": "success",
    "success": true,
    "requestId": null,
    "orgInfoList": [
        {
            "companyId": 963,
            "companyName": "Columbia Energy",
            "roleName": "Administrator"
        },
        {
            "companyId": 999,
            "companyName": "Green Energy",
            "roleName": "Developer"
        }
    ]
}
```

Sample Code in Python

```
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/account/info'

    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'bearer eyJhbGciOiJSUzI1NIkpXVCJ9.eyJhdWQiOlsib2F1dGgyLXJl'
    }
    data = {}
    response = requests.post(url, headers=headers, json=data)
    print(response.status_code)
    print(response.json())
```

3. Invoke {baseUrl}/v1.0/account/token?appId={AppId} (you obtained from creating application) with the filed 'companyId' to get a business member access token

Sample Request:

```
curl --location 'localhost:8019/v1.0/account/token?appId=fffffff' --header 'Content-Type: application/json' --data-raw '{
  "appSecret": "ffffff",
  "email": "<EMAIL>", #email of DeyeCloud account
  "companyId":999,
  "password": "1d8286c46e8e0d32cab58abcg32f778642be"  #password of ${title} account
}'
```

Sample Response:

```
{
    "code": "1000000",
    "msg": "success",
    "success": true,
    "requestId": null,
    "accessToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOY25fMiIsWwiOnsOjk2MCwidG9wR3JvdXBJZCI6MSwiZ3JvdXBJZCI6MSwicm9sZUlkIjoyMCwidXNlcklkIjo1LCJ2ZXJzaW9uIjoxMDAwLCJpZGVudGlmaWVyIjoiemhhbmd5dW5xaW5nQGRleWUuY29tLmNuIiwiaWRlbnRpdHlUeXBlIjoyLCJtZGMiOiJ0ZXN0IiwiYXBwSWQiOiIwMDAwMDAwMDAwMDAwMDAifSwiZXhwIjoxNzI0MDQyOTU0LCJtZGMiOiJ0ZXN0IiwiYXV0aG9yaXRpZXMiOlsiYWxsIl0sImp0aSI6IjYzNTNkMTU3LWQwOTMtNDhiYi1hZWQ0LWRmOTkxM2NmYjNkMiIsImNsaWVudF9pZCI6InRlc3QifQ.io074oUB2EW26ygPNjmPDdKZ7N-QTH381UtoUGls8RXQwkqaE2aRI1DZiVPn4Dow05DuPX7-FC-wz4AtqTKQO3F7nLk-EMRiSs_6XFRGIK5hUr4nB0ii_uMU1zaaLq4GMYugKgrzY88GV7Nm237uZj3VTFW6tC6LN4UNCmFaPpNBk5v6z2ZGjmYJKVicveFB2hV3ptAMVGwhJLmzPpuYjBwMJhl9e5KTo-lh-ibOzX-SdXe941x-sWpBdMeRcI83V-935_CqzhQTD29P1v5uOQno1n8MClyFVxtl_N7dIcsI9pRozYWOneLDr7ETgC20Wy4s2OhN8hGGCQtHo6iipQ",
    "tokenType": "bearer",
    "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQi3l1bnFpbmdZDE1Ny1kMDkzLTQ4YmItYWVkNC1kZjk5MTNjZmIzZDIiLCJkZXRhaWwiOnsib3JnYW5pemF0aW9uSWQiOjk2MCwidG9wR3JvdXBJZCI6MSwiZ3JvdXBJZCI6MSwicm9sZUlkIjoyMCwidXNlcklkIjo1LCJ2ZXJzaW9uIjoxMDAwLCJpZGVudGlmaWVyIjoiemhhbmd5dW5xaW5nQGRleWUuY29tLmNuIiwiaWRlbnRpdHlUeXBlIjoyLCJtZGMiOiJ0ZXN0IiwiYXBwSWQiOiIwMDAwMDAwMDAwMDAwMDAifSwiZXhwIjoxNzI0MDQyOTU0LCJtZGMiOiJ0ZXN0IiwiYXV0aG9yaXRpZXMiOlsiYWxsIl0sImp0aSI6ImEzN2JlZDMwLTVmYmYtNGIyNi05NzdhLTY2N2IyNjg1NmNjNiIsImNsaWVudF9pZCI6InRlc3QifQ.euMIZwauzQHmCJr4s51NHUKGJdWdX5uKjCS6OGhdjzrqhOqG8R0P72xmuovUstF6Op-3zGPfelz6RSD2a_PrLxoXIXV4Ju9qqRqNLN-b9rbC-n3vnhZoo5_ir05EE37zKnq7e_MVwjeKy_aFiib-JpZ4AqnVxvk6AlNgb059N8W8KNHtZi_l1ZDSz_tAp7fPJUC4vcdCmtJVSiSDXJrBi6gvojHysBK6HSuKT8gTiTxDimbkrg1YH7xWziaZQQIB-5w",
    "expiresIn": "5183999",
    "scope": "all",
    "uid": 1000
}
```

Sample Code in Python

Obtain business member token through email

```
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=***************'
    headers = {
        'Content-Type': 'application/json'
    }
    # Body
    data = {
        "appSecret": "af958493485e56e7d58d092ea3473b64",
        "email": "<EMAIL>",  #email of DeyeCloud account
        "password": "1d8286c46e8e0d32cab580009114ad8aa33180578642be",  #password of DeyeCloud account
        "companyId":"96011"
    }
    try:
        # Send POST Request 
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  
        # print response status
        print(response.status_code)
        print(response.json())

    except requests.exceptions.HTTPError as err:
        print(f"HTTP error occurred: {err}")
    except Exception as err:
        print(f"Other error occurred: {err}")
```

Obtain business member access token through username

```
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=***************'
    headers = {
        'Content-Type': 'application/json'
    }
    data = {
        "appSecret": "af958493485e56e7d58d092ea3473b64",
        "username": "jbdon",  #username of DeyeCloud account
        "password": "8d969eef6ecad3c29a00000000e686cf0c3f5d0000002"   #password of DeyeCloud account
        "companyId":"96011"
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  
        print(response.status_code)
        print(response.json())

    except requests.exceptions.HTTPError as err:
        print(f"HTTP error occurred: {err}")
    except Exception as err:
        print(f"Other error occurred: {err}")
```

Obtain business member access token through mobile

```
import requests

if __name__ == '__main__':
    url = 'https://eu1-developer.deyecloud.com/v1.0/account/token?appId=***************'
    headers = {
        'Content-Type': 'application/json'
    }
    data = {
        "appSecret": "af958493485e56e7d58d092ea3473b64",
        "mobile": "***********",  #mobile of DeyeCloud account
        "countryCode": 86,  #countryCode of DeyeCloud account
        "password": "1d8286c4600000008a809114adf778642be",  #password of DeyeCloud account
        "companyId":"96011"
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  
        print(response.status_code)
        print(response.json())

    except requests.exceptions.HTTPError as err:
        print(f"HTTP error occurred: {err}")
    except Exception as err:
        print(f"Other error occurred: {err}")
```

![Image 3](blob:http://localhost/e7ff688ff0efc0d604c90edf11a5276e)

Notice:

It is recommended that you remember the companyId so that you don’t have to fetch it again when you obtain the access token again when it expires.

Commit the command to control the device

Set Time of Use of the Device

Sample Code in Python

```
import requests
if __name__ == '__main__':

    url = 'https://eu1-developer.deyecloud.com/v1.0/order/sys/tou/update'
    headers = {
        'Authorization': 'bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQlk7iyBCwwUQuXVlsg1vT70dgh8hlctEwm5jSsZQ',
        'Content-Type': 'application/json'
    }

    data = {
        "deviceSn": "221299",
        "timeUseSettingItems": [
            {
                "enableGeneration": True,
                "enableGridCharge": True,
                "power": 1000,
                "soc": 20,
                "time": "02:10"
            },
            {
                "enableGeneration": True,
                "enableGridCharge": False,
                "power": 2000,
                "soc": 20,
                "time": "03:15"
            },
            {
                "enableGeneration": False,
                "enableGridCharge": True,
                "power": 3000,
                "soc": 40,
                "time": "04:20"
            },
            {
                "enableGeneration": True,
                "enableGridCharge": True,
                "power": 4000,
                "soc": 50,
                "time": "05:25"
            },
            {
                "enableGeneration": False,
                "enableGridCharge": True,
                "power": 333,
                "soc": 60,
                "time": "14:00"
            },
            {
                "enableGeneration": True,
                "enableGridCharge": False,
                "power": 100,
                "soc": 70,
                "time": "15:10"
            }
        ],
        "timeoutSeconds": 30
    }
    response = requests.post(url, headers=headers, json=data)

    print(response.status_code)
    print(response.json())
```

Obtain the command commission result based on orderId

Sample Code in Python

```
import requests

if __name__ == '__main__':

    url = 'https://eu1-developer.deyecloud.com/v1.0/order/6811105' # 6811105 is the orderId in the reponse of 

    headers = {
        'Authorization': 'bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJhwidXNlckjoiemhhbmd5dW5xaW5nQGRleWUuY29tLmNuIiwiaWRlbnRpdHlUeXBlIjoyLCJtZGMiOiJ0ZXN0IiwiYXBwSWQiOiIwMDAwMDAwMDAwMDAwMDAifSwiZXhwIjoxNzI0NDgyOTY5LCJtZGMiOiJ0ZXN0IiwiYXV0aG9yaXRpZXMiOlsiYWxsIl0sImp0aSI6IjNjYjY5OGQ3LTlhZjItNGU2ZC1iODdmLWE2ZWM3MDdkMDdiNSIsImNsaWVudF9pZCI6InRlc3QifQ.hN5V8P2Yvog6ll-_49MIlkOwY1E2q0TBTb4zBbQy4rrCnFqITFRbBdGoozD-7c_K_IqFfSM4-x5HZiSHRjUzeCfx55aiXHI0RLJyQoeUHddHEsMfzzCrBHEUTEkWOtiEl03ctnJi4N067tSVU2IJiIxxk3fN3zlaiVCvbHEf2ZztEnB-VJE0LO8y-xOQlGGbZuHmEzM1gnSc1qVbeaOEQO6Wqr8EPcaM3ry_v7hjzwrIEPa0xEvOApuTk4iX3OUm8JlKhiM3j-Wlk7iyBCwwUKx40ncTpTK1qN9DRtDzNoG-OUntCZZpKmTTqQuXVlsg1vT70dgh8hlctEwm5jSsZQ',
        'Content-Type': 'application/json;charset=UTF-8'
    }

    response = requests.get(url, headers=headers)

    print(response.status_code)
    print(response.json())
```

For Details See [API Documentation](https://developer.deyecloud.com/api).

