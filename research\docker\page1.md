﻿Title: Get started

URL Source: https://docs.docker.com/get-started/

Published Time: 2025-05-30 18:18:27 +0200 +0200

Markdown Content:
Get started | Docker Docs 

===============

![Image 1](https://www.docker.com/app/uploads/2024/01/icon-new.svg)

[Building AI Agents is Now Easy](https://www.docker.com/blog/build-ai-agents-with-docker-compose/)

✕

[](https://docs.docker.com/ "Docker Docs home page")

*   [Get started](https://docs.docker.com/get-started/)
*   [Guides](https://docs.docker.com/guides/)
*   [Manuals](https://docs.docker.com/manuals/)
*   [Reference](https://docs.docker.com/reference/)

[](https://docs.docker.com/search)

K

Start typing to search… or try Ask AI

Ask AI![Image 2: AI Stars](https://docs.docker.com/images/ai-stars.svg)

 Back

[Get started](https://docs.docker.com/get-started/)

*   [Guides](https://docs.docker.com/guides/)
*   [Manuals](https://docs.docker.com/manuals/)
*   [Reference](https://docs.docker.com/reference/)

* * *

This section

*   [Get Docker](https://docs.docker.com/get-started/get-docker/ "Get Docker")
*   [What is Docker?](https://docs.docker.com/get-started/docker-overview/ "What is Docker?")
*   [Introduction](https://docs.docker.com/get-started/introduction/)  
    *   [Get Docker Desktop](https://docs.docker.com/get-started/introduction/get-docker-desktop/ "Get Docker Desktop")
    *   [Develop with containers](https://docs.docker.com/get-started/introduction/develop-with-containers/ "Develop with containers")
    *   [Build and push your first image](https://docs.docker.com/get-started/introduction/build-and-push-first-image/ "Build and push your first image")
    *   [What's next](https://docs.docker.com/get-started/introduction/whats-next/ "What's next")

*    Docker concepts  
    *    The basics  
        *   [What is a container?](https://docs.docker.com/get-started/docker-concepts/the-basics/what-is-a-container/ "What is a container?")
        *   [What is an image?](https://docs.docker.com/get-started/docker-concepts/the-basics/what-is-an-image/ "What is an image?")
        *   [What is a registry?](https://docs.docker.com/get-started/docker-concepts/the-basics/what-is-a-registry/ "What is a registry?")
        *   [What is Docker Compose?](https://docs.docker.com/get-started/docker-concepts/the-basics/what-is-docker-compose/ "What is Docker Compose?")

    *   [Building images](https://docs.docker.com/get-started/docker-concepts/building-images/)  
        *   [Understanding the image layers](https://docs.docker.com/get-started/docker-concepts/building-images/understanding-image-layers/ "Understanding the image layers")
        *   [Writing a Dockerfile](https://docs.docker.com/get-started/docker-concepts/building-images/writing-a-dockerfile/ "Writing a Dockerfile")
        *   [Build, tag, and publish an image](https://docs.docker.com/get-started/docker-concepts/building-images/build-tag-and-publish-an-image/ "Build, tag, and publish an image")
        *   [Using the build cache](https://docs.docker.com/get-started/docker-concepts/building-images/using-the-build-cache/ "Using the build cache")
        *   [Multi-stage builds](https://docs.docker.com/get-started/docker-concepts/building-images/multi-stage-builds/ "Multi-stage builds")

    *    Running containers  
        *   [Publishing and exposing ports](https://docs.docker.com/get-started/docker-concepts/running-containers/publishing-ports/ "Publishing and exposing ports")
        *   [Overriding container defaults](https://docs.docker.com/get-started/docker-concepts/running-containers/overriding-container-defaults/ "Overriding container defaults")
        *   [Persisting container data](https://docs.docker.com/get-started/docker-concepts/running-containers/persisting-container-data/ "Persisting container data")
        *   [Sharing local files with containers](https://docs.docker.com/get-started/docker-concepts/running-containers/sharing-local-files/ "Sharing local files with containers")
        *   [Multi-container applications](https://docs.docker.com/get-started/docker-concepts/running-containers/multi-container-applications/ "Multi-container applications")

*   [Docker workshop](https://docs.docker.com/get-started/workshop/)  
    *   [Part 1: Containerize an application](https://docs.docker.com/get-started/workshop/02_our_app/ "Part 1: Containerize an application")
    *   [Part 2: Update the application](https://docs.docker.com/get-started/workshop/03_updating_app/ "Part 2: Update the application")
    *   [Part 3: Share the application](https://docs.docker.com/get-started/workshop/04_sharing_app/ "Part 3: Share the application")
    *   [Part 4: Persist the DB](https://docs.docker.com/get-started/workshop/05_persisting_data/ "Part 4: Persist the DB")
    *   [Part 5: Use bind mounts](https://docs.docker.com/get-started/workshop/06_bind_mounts/ "Part 5: Use bind mounts")
    *   [Part 6: Multi-container apps](https://docs.docker.com/get-started/workshop/07_multi_container/ "Part 6: Multi-container apps")
    *   [Part 7: Use Docker Compose](https://docs.docker.com/get-started/workshop/08_using_compose/ "Part 7: Use Docker Compose")
    *   [Part 8: Image-building best practices](https://docs.docker.com/get-started/workshop/09_image_best/ "Part 8: Image-building best practices")
    *   [Part 9: What next](https://docs.docker.com/get-started/workshop/10_what_next/ "Part 9: What next")

*   [Educational resources](https://docs.docker.com/get-started/resources/ "Educational resources")

[Home](https://docs.docker.com/)/Get started
Get started
===========

* * *

If you're new to Docker, this section guides you through the essential resources to get started.

Follow the guides to help you get started and learn how Docker can optimize your development workflows.

For more advanced concepts and scenarios in Docker, see [Guides](https://docs.docker.com/guides/).

[Foundations of Docker](https://docs.docker.com/get-started/#foundations-of-docker)
-----------------------------------------------------------------------------------

Install Docker and jump into discovering what Docker is.

[### Get Docker Choose the best installation path for your setup.](https://docs.docker.com/get-started/get-docker/)

[### What is Docker? Learn about the Docker platform.](https://docs.docker.com/get-started/docker-overview/)

Learn the foundational concepts and workflows of Docker.

[### Introduction Get started with the basics and the benefits of containerizing your applications.](https://docs.docker.com/get-started/introduction/)

[### Docker concepts Gain a better understanding of foundational Docker concepts.](https://docs.docker.com/get-started/docker-concepts/the-basics/what-is-a-container/)

[### Docker workshop Get guided through a 45-minute workshop to learn about Docker.](https://docs.docker.com/get-started/workshop/)

[Contact support](https://hub.docker.com/support/contact)

[Product offerings](https://www.docker.com/)[Pricing](https://www.docker.com/pricing/)[About us](https://www.docker.com/company/)[Contribute](https://docs.docker.com/contribute/)[Read llms.txt](https://docs.docker.com/llms.txt)

* * *

Copyright © 2013-2025 Docker Inc. All rights reserved.

[](http://twitter.com/docker/ "X (Twitter)")[](https://www.linkedin.com/company/docker "LinkedIn")[](https://www.instagram.com/dockerinc/ "Instagram")[](http://www.youtube.com/user/dockerrun "YouTube")[](https://www.facebook.com/docker.run "Facebook")

[Terms of Service](https://www.docker.com/legal/docker-terms-service "Docker Terms of Service")[Status](https://www.dockerstatus.com/ "Docker Systems Status Page")[Legal](https://www.docker.com/legal "Docker Legal Terms")

Cookies Settings

Theme:Light Dark

Give feedback

Was this page useful?
---------------------

Hate Love

Next

By clicking “Accept All Cookies”, you agree to the storing of cookies on your device to enhance site navigation, analyze site usage, and assist in our marketing efforts. 

Cookies Settings Reject All Accept All Cookies

![Image 3: Company Logo](https://cdn.cookielaw.org/logos/static/ot_company_logo.png)

Privacy Preference Center
-------------------------

When you visit any website, it may store or retrieve information on your browser, mostly in the form of cookies. This information might be about you, your preferences or your device and is mostly used to make the site work as you expect it to. The information does not usually directly identify you, but it can give you a more personalized web experience. Because we respect your right to privacy, you can choose not to allow some types of cookies. Click on the different category headings to find out more and change our default settings. However, blocking some types of cookies may impact your experience of the site and the services we are able to offer. 

[More information](https://cookiepedia.co.uk/giving-consent-to-cookies)

Allow All
### Manage Consent Preferences

#### Functional Cookies

- [x] Functional Cookies 

These cookies enable the website to provide enhanced functionality and personalisation. They may be set by us or by third party providers whose services we have added to our pages. If you do not allow these cookies then some or all of these services may not function properly.

#### Strictly Necessary Cookies

Always Active

These cookies are necessary for the website to function and cannot be switched off in our systems. They are usually only set in response to actions made by you which amount to a request for services, such as setting your privacy preferences, logging in or filling in forms. You can set your browser to block or alert you about these cookies, but some parts of the site will not then work. These cookies do not store any personally identifiable information.

#### Performance Cookies

- [x] Performance Cookies 

These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us to know which pages are the most and least popular and see how visitors move around the site. All information these cookies collect is aggregated and therefore anonymous. If you do not allow these cookies we will not know when you have visited our site, and will not be able to monitor its performance.

#### Targeting Cookies

- [x] Targeting Cookies 

These cookies may be set through our site by our advertising partners. They may be used by those companies to build a profile of your interests and show you relevant adverts on other sites. They do not store directly personal information, but are based on uniquely identifying your browser and internet device. If you do not allow these cookies, you will experience less targeted advertising.

### Cookie List

Clear

- [x] checkbox label label

Apply Cancel

Consent Leg.Interest

- [x] checkbox label label

- [x] checkbox label label

- [x] checkbox label label

Reject All Confirm My Choices

[![Image 4: Powered by Onetrust](https://cdn.cookielaw.org/logos/static/powered_by_logo.svg)](https://www.onetrust.com/products/cookie-consent/)

