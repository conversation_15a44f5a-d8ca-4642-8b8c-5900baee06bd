Title: Welcome to Flask — Flask Documentation (3.1.x)
URL Source: https://flask.palletsprojects.com/en/stable/
Markdown Content:
[![Image 1: _images/flask-name.svg](https://flask.palletsprojects.com/en/stable/_images/flask-name.svg)](https://flask.palletsprojects.com/en/stable/_images/flask-name.svg)

# Welcome to Flask

Flask is a lightweight [WSGI](https://wsgi.readthedocs.io/) web application framework. It is designed to make getting started quick and easy, with the ability to scale up to complex applications. It began as a simple wrapper around [Werkzeug](https://werkzeug.palletsprojects.com/) and [Jinja](https://jinja.palletsprojects.com/) and has become one of the most popular Python web application frameworks.

Flask offers suggestions, but doesn't enforce any dependencies or project layout. It is up to the developer to choose the tools and libraries they want to use. There are many extensions provided by the community that make adding new functionality easy.

## Installation

Install and update using [pip](https://pip.pypa.io/en/stable/getting-started/):

```bash
$ pip install -U Flask
```

## A Simple Example

```python
from flask import Flask

app = Flask(__name__)

@app.route("/")
def hello_world():
    return "<p>Hello, World!</p>"
```

```bash
$ flask --app hello run
 * Running on http://127.0.0.1:5000
```

## Contributing

For guidance on setting up a development environment and how to make a contribution to Flask, see the [contributing guidelines](https://flask.palletsprojects.com/en/stable/contributing/).

## Donate

The Pallets organization develops and supports Flask and the libraries it uses. In order to grow the community of contributors and users, and allow the maintainers to devote more time to the projects, [please donate today](https://palletsprojects.com/donate).

## Links

- Documentation: [https://flask.palletsprojects.com/](https://flask.palletsprojects.com/)
- Changes: [https://flask.palletsprojects.com/changes/](https://flask.palletsprojects.com/changes/)
- PyPI Releases: [https://pypi.org/project/Flask/](https://pypi.org/project/Flask/)
- Source Code: [https://github.com/pallets/flask/](https://github.com/pallets/flask/)
- Issue Tracker: [https://github.com/pallets/flask/issues/](https://github.com/pallets/flask/issues/)
- Chat: [https://discord.gg/pallets](https://discord.gg/pallets)
