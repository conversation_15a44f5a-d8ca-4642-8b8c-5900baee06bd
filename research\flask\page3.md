Title: API — Flask Documentation (3.1.x)
URL Source: https://flask.palletsprojects.com/en/stable/api/

# Flask API Documentation

[Note: This is a comprehensive API reference - truncated for brevity but includes all major Flask classes and functions]

## Flask Application

### class flask.Flask(import_name, static_url_path=None, static_folder='static', static_host=None, host_matching=False, subdomain_matching=False, template_folder='templates', instance_relative_config=False, instance_path=None, root_path=None)

The Flask object implements a WSGI application and acts as the central object. It is passed the name of the module or package of the application. Once it is created it will act as a central registry for the view functions, the URL rules, template configuration and much more.

The name of the package is used to resolve resources from inside the package or the folder the module is contained in depending on if the package parameter resolves to an actual python package (a folder with an `__init__.py` file inside) or a standard module (just a `.py` file).

#### Parameters:
- **import_name** (str) – the name of the application package
- **static_url_path** (str | None) – can be used to specify a different path for the static files on the web
- **static_folder** (str | os.PathLike[str] | None) – The folder with static files
- **static_host** (str | None) – the host to use when adding the static route
- **host_matching** (bool) – if set to True this switches to host matching mode
- **subdomain_matching** (bool) – if set to True this will enable subdomain matching
- **template_folder** (str | os.PathLike[str] | None) – the folder that contains the templates
- **instance_relative_config** (bool) – if set to True the config object will load the instance config
- **instance_path** (str | None) – An alternative instance path for the application
- **root_path** (str | None) – The path to the root of the application files

#### Key Methods:

##### route(rule, **options)
A decorator that is used to register a view function for a given URL rule.

```python
@app.route('/')
def index():
    return 'Hello World'
```

##### add_url_rule(rule, endpoint=None, view_func=None, provide_automatic_options=None, **options)
Connects a URL rule. Works exactly like the route() decorator.

##### run(host=None, port=None, debug=None, load_dotenv=True, **options)
Runs the application on a local development server.

**Warning**: Do not use run() in a production setting. It is not designed to be particularly stable, fast, or secure.

##### test_client(use_cookies=True, **kwargs)
Creates a test client for this application.

##### test_request_context(*args, **kwargs)
Creates a WSGI environment from the given values.

## Request Handling

### class flask.Request(environ, populate_request=True, shallow=False)

The request object used by default in Flask. Remembers the matched endpoint and view arguments.

#### Key Attributes:
- **method** – The request method (GET, POST, etc.)
- **form** – A MultiDict with the parsed form data from POST or PUT requests
- **args** – A MultiDict with the parsed contents of the query string
- **files** – A MultiDict with files uploaded in the request
- **cookies** – A dict with all cookie values
- **headers** – The headers from the request
- **json** – The parsed JSON data if mimetype indicates JSON
- **url** – The full URL that was requested
- **base_url** – The URL without the query string

### flask.request
The current request object. This is a local proxy and cannot be used outside of a request context.

## Response Handling

### class flask.Response(response=None, status=None, headers=None, mimetype=None, content_type=None, direct_passthrough=False)

The response object that is used by default in Flask.

#### Key Methods:

##### set_cookie(key, value='', max_age=None, expires=None, path='/', domain=None, secure=False, httponly=False, samesite=None)
Sets a cookie.

### flask.make_response(*args)
Converts the return value from a view function to a Response object.

## Templating

### flask.render_template(template_name_or_list, **context)
Renders a template from the template folder with the given context.

### flask.render_template_string(source, **context)
Renders a template from the given template source string.

## URL Handling

### flask.url_for(endpoint, **values)
Generates a URL to the given endpoint with the method provided.

### flask.redirect(location, code=302, Response=None)
Returns a response object that redirects the client to the target location.

## Error Handling

### flask.abort(code, *args, **kwargs)
Raises an HTTPException for the given status code.

### flask.Flask.errorhandler(code_or_exception)
A decorator that is used to register a function that handles errors.

```python
@app.errorhandler(404)
def page_not_found(error):
    return render_template('page_not_found.html'), 404
```

## Session Interface

### flask.session
The session object works pretty much like an ordinary dict, with the difference that it keeps track of modifications.

This is a proxy. See Notes On Proxies for more information.

## Application Context

### flask.current_app
Points to the application handling the request. This is useful for extensions that want to support multiple applications.

### flask.g
A namespace object that can store data during an application context.

### flask.has_app_context()
Returns True if an application context is available.

### flask.Flask.app_context()
Binds an application context.

## Request Context

### flask.has_request_context()
Returns True if a request context is available.

### flask.Flask.request_context(environ)
Creates a RequestContext from the given environment.

## JSON Support

### flask.json.jsonify(*args, **kwargs)
Creates a Response with the JSON representation of the given arguments.

## Message Flashing

### flask.flash(message, category='message')
Flashes a message to the next request.

### flask.get_flashed_messages(with_categories=False, category_filter=None)
Pulls all flashed messages from the session.

## Signals

Flask includes signal support based on the Blinker library.

### flask.template_rendered
This signal is sent when a template was successfully rendered.

### flask.before_render_template
This signal is sent before template rendering.

### flask.request_started
This signal is sent when the request context is set up.

### flask.request_finished
This signal is sent right before the response is sent to the client.

### flask.got_request_exception
This signal is sent when an unhandled exception happens during request processing.

### flask.request_tearing_down
This signal is sent when the request is tearing down.

### flask.appcontext_tearing_down
This signal is sent when the app context is tearing down.

### flask.appcontext_pushed
This signal is sent when an application context is pushed.

### flask.appcontext_popped
This signal is sent when an application context is popped.

### flask.message_flashed
This signal is sent when the application is flashing a message.

## Class-Based Views

### class flask.views.View
Subclass this class and override dispatch_request() to create a generic class-based view.

### class flask.views.MethodView
Dispatches request methods to the corresponding instance methods.

## URL Route Registrations

Generally there are three ways to define rules for the routing system:

1. You can use the flask.Flask.route() decorator.
2. You can use the flask.Flask.add_url_rule() function.
3. You can directly access the underlying Werkzeug routing system.

Variable parts in the route can be specified with angular brackets (`/user/<username>`). The following converters are available:

- `string` - accepts any text without a slash (default)
- `int` - accepts integers
- `float` - like int but for floating point values
- `path` - like string but also accepts slashes
- `any` - matches one of the items provided
- `uuid` - accepts UUID strings

## Command Line Interface

### class flask.cli.FlaskGroup
Special subclass of the AppGroup group that supports loading more commands from the configured Flask app.

### class flask.cli.AppGroup
This works similar to a regular click Group but changes the behavior of the command() decorator.

### flask.cli.with_appcontext(f)
Wraps a callback so that it's guaranteed to be executed with the script's application context.

### flask.cli.pass_script_info(f)
Marks a function so that an instance of ScriptInfo is passed as first argument.

This comprehensive API reference covers all the major Flask classes, functions, and utilities needed for web application development.
