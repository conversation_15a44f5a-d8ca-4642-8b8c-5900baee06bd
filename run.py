#!/usr/bin/env python3
"""
Solar Dashboard Application Entry Point
This script initializes and runs the Flask application.

Usage:
    python run.py                          # Development server
    gunicorn -c gunicorn.conf.py run:app   # Production server
    
Environment Variables:
    FLASK_ENV: development/production (default: production)
    FLASK_DEBUG: 0/1 (default: 0)
    PORT: Port to run on (default: 5000)
    HOST: Host to bind to (default: 0.0.0.0)
"""

import os
import sys
from pathlib import Path

# Add the application root to Python path
app_root = Path(__file__).parent
sys.path.insert(0, str(app_root))

from app import create_app, db
from app.models import Station, Device, StationMeasurement, DeviceMeasurement
from flask import current_app
import logging

def create_application():
    """Create and configure the Flask application."""
    app = create_app()
    
    # Configure logging for production
    if not app.debug and not app.testing:
        configure_logging(app)
    
    return app

def configure_logging(app):
    """Configure application logging for production."""
    import logging
    from logging.handlers import RotatingFileHandler
    import os
    
    # Create logs directory
    if not os.path.exists('logs'):
        os.mkdir('logs')
    
    # Setup file handler
    file_handler = RotatingFileHandler(
        'logs/solar_dashboard.log',
        maxBytes=10240000,  # 10MB
        backupCount=10
    )
    
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s %(name)s %(threadName)s : %(message)s '
        '[in %(pathname)s:%(lineno)d]'
    ))
    
    # Set logging level based on environment
    log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
    file_handler.setLevel(getattr(logging, log_level, logging.INFO))
    app.logger.addHandler(file_handler)
    
    app.logger.setLevel(getattr(logging, log_level, logging.INFO))
    app.logger.info('Solar Dashboard startup')

def init_database(app):
    """Initialize database tables if they don't exist."""
    with app.app_context():
        try:
            # Create all tables
            db.create_all()
            current_app.logger.info("Database tables created successfully")
            
            # Check if we need to run migrations
            from flask_migrate import upgrade
            try:
                upgrade()
                current_app.logger.info("Database migrations applied successfully")
            except Exception as e:
                current_app.logger.warning(f"No migrations to apply or migration failed: {e}")
                
        except Exception as e:
            current_app.logger.error(f"Database initialization failed: {e}")
            raise

def register_cli_commands(app):
    """Register custom CLI commands."""
    
    @app.cli.command('init-db')
    def init_db_command():
        """Initialize the database."""
        init_database(app)
        print("Database initialized successfully!")
    
    @app.cli.command('create-user')
    def create_user_command():
        """Create a test user (for future authentication)."""
        print("User creation not implemented yet.")
    
    @app.cli.command('sync-deye')
    def sync_deye_command():
        """Manually sync data from Deye API."""
        from app.scheduler.tasks import collect_live_data, collect_daily_data
        
        print("Starting manual Deye API sync...")
        try:
            collect_live_data()
            print("Live data sync completed successfully!")
        except Exception as e:
            print(f"Live data sync failed: {e}")
        
        try:
            collect_daily_data()
            print("Daily data sync completed successfully!")
        except Exception as e:
            print(f"Daily data sync failed: {e}")
    
    @app.cli.command('test-api')
    def test_api_command():
        """Test Deye API connection."""
        from app.api.deye_client import DeyeCloudClient
        
        try:
            client = DeyeCloudClient()
            stations = client.get_station_list()
            print(f"API test successful! Found {len(stations.get('data', []))} stations.")
            
            for station in stations.get('data', []):
                print(f"  - {station.get('name', 'Unknown')} (ID: {station.get('id')})")
                
        except Exception as e:
            print(f"API test failed: {e}")

def main():
    """Main application entry point."""
    app = create_application()
    
    # Register CLI commands
    register_cli_commands(app)
    
    # Initialize database in development
    if app.config.get('FLASK_ENV') == 'development':
        init_database(app)
    
    # Get host and port from environment
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = app.config.get('DEBUG', False)
    
    # Log startup information
    app.logger.info(f"Starting Solar Dashboard on {host}:{port}")
    app.logger.info(f"Environment: {app.config.get('FLASK_ENV', 'production')}")
    app.logger.info(f"Debug mode: {debug}")
    
    if __name__ == '__main__':
        # Development server
        app.run(
            host=host,
            port=port,
            debug=debug,
            use_reloader=debug,
            threaded=True
        )
    
    return app

# Create application instance for WSGI servers (gunicorn, etc.)
app = create_application()

# Initialize database for production deployments
if app.config.get('FLASK_ENV') == 'production':
    with app.app_context():
        try:
            init_database(app)
        except Exception as e:
            app.logger.error(f"Failed to initialize database: {e}")

if __name__ == '__main__':
    main()
